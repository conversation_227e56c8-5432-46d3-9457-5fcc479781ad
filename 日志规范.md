# collectloop项目日志规范

## 1. 规范目标

本规范旨在统一collectloop项目的日志记录方式，提高日志质量和可用性，便于系统监控、问题排查和性能分析。

## 2. 日志分类

根据业务需求，项目日志分为以下两类：

### 2.1 正式日志

- **定义**：业务运行过程中持久化存储的结构化日志
- **存储位置**：文件系统、Elasticsearch
- **记录方式**：通过std封装的结构化日志API
- **使用场景**：业务流程记录、错误异常、安全审计

### 2.2 调试日志

- **定义**：开发测试过程中的临时输出信息
- **存储位置**：标准输出/标准错误
- **记录方式**：通过log.Println或fmt.Println
- **使用场景**：本地调试、配置信息显示

## 3. 日志级别

### 3.1 级别定义

| 级别 | 数值 | 使用场景 | 示例 |
|------|------|----------|------|
| TRACE | 0 | 最详细的跟踪信息 | 函数进入/退出、参数值变化 |
| DEBUG | 1 | 调试信息 | 中间计算结果、临时状态 |
| INFO | 2 | 正常运行信息 | 服务启动/停止、任务完成 |
| WARN | 3 | 潜在问题警告 | 配置次优、性能下降 |
| ERROR | 4 | 错误但不影响运行 | 单次操作失败、重试成功 |
| FATAL | 5 | 严重错误导致退出 | 数据库连接失败、配置错误 |

### 3.2 环境配置

| 环境 | 推荐日志级别 | 说明 |
|------|--------------|------|
| 开发环境 | DEBUG (1) | 提供详细信息辅助开发 |
| 测试环境 | INFO (2) | 记录主要流程不含调试信息 |
| 生产环境 | WARN (3) | 只记录警告和错误信息 |

## 4. 日志内容规范

### 4.1 正式日志内容

每条正式日志必须包含以下要素：

1. **基础字段**
   - 时间戳：精确到毫秒
   - 日志级别：明确显示日志级别
   - 模块名称：产生日志的模块
   - 请求追踪ID：关联同一请求的所有日志

2. **上下文字段**（使用Fields结构）
   - `topic`: 日志主题，表示日志所属业务领域
   - `tlClientId`: 医院客户端ID
   - `status`: 数据状态或处理阶段
   - 其他业务相关字段

3. **消息内容**
   - 简明扼要描述事件
   - 包含关键参数值
   - 错误日志必须包含错误信息

### 4.2 调试日志内容

调试日志应遵循以下规范：

1. **简明前缀**：标识日志来源或类型
2. **关键信息**：变量值、状态描述
3. **格式一致**：使用冒号分隔前缀和内容

## 5. 日志记录规范

### 5.1 正式日志API

使用std封装的结构化日志API记录正式日志：

```go
// 信息级别日志
std.LogInfoAndFields(logrus.Fields{
    "topic":      "writeRedis",
    "tlClientId": tid,
    "status":     "In",
    "count":      len(data),
}, "同步患者数据到Redis完成")

// 错误级别日志
std.LogErrorLnAndFields(logrus.Fields{
    "topic":      "dbQuery",
    "tlClientId": tid,
    "sql":        queryString,
}, "查询患者数据失败", err)
```

### 5.2 调试日志API

使用标准库函数记录调试日志：

```go
// 配置信息
log.Println("Database:", DbName)

// 调试信息
fmt.Println("Starting database connection")
```

### 5.3 日志使用场景

#### 5.3.1 必须使用正式日志的场景

- 业务关键流程：数据同步、状态变更
- 所有错误和异常情况
- 安全相关事件：认证、授权
- 系统状态变化：启动、关闭、重连

#### 5.3.2 适合使用调试日志的场景

- 应用启动时的配置展示
- 开发阶段的状态检查
- 临时性的问题诊断

### 5.4 敏感信息处理

对于敏感信息，必须进行脱敏处理后再记录：

- 身份证号：保留前6位和后4位，中间用星号代替
- 手机号：保留前3位和后4位，中间用星号代替
- 密码和密钥：不得记录，使用固定占位符代替

## 6. 日志存储与管理

### 6.1 文件日志

#### 6.1.1 存储位置

- **路径**：/opt/log/ivankadedicated-api/
- **命名格式**：`YYYY-MM-DD.log`

#### 6.1.2 日志轮转

- **轮转策略**：每日轮转
- **保留策略**：保留30天
- **实现方式**：应用内置轮转 + logrotate备份

#### 6.1.3 logrotate配置

```
/opt/log/ivankadedicated-api/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0644 appuser appgroup
    postrotate
        [ -s /var/run/collectloop.pid ] && kill -USR1 `cat /var/run/collectloop.pid`
    endscript
}
```

### 6.2 Elasticsearch日志

#### 6.2.1 配置参数

- **集群地址**：http://*************:9200
- **用户认证**：elastic/xdkj123!@#
- **索引命名**：
  - 操作日志：ivankadedicated-api_operation-YYYY.MM.DD
  - 机器人日志：ivankadedicated-api_robot-YYYY.MM.DD

#### 6.2.2 索引管理

- **保留策略**：90天
- **定期归档**：使用ILM策略
- **节点均衡**：设置合理的分片数

#### 6.2.3 日志结构映射

确保以下字段正确映射：
- `@timestamp`: date类型
- `level`: keyword类型
- `topic`: keyword类型
- `tlClientId`: keyword类型
- `message`: text类型，支持全文搜索

## 7. 业务场景日志实践

### 7.1 患者数据同步

```go
// 同步开始
std.LogInfoAndFields(logrus.Fields{
    "topic":      "patientSync",
    "tlClientId": tlid,
    "status":     "start",
    "syncType":   syncType,  // In/Out/Up
}, "开始同步患者数据")

// 同步完成
std.LogInfoAndFields(logrus.Fields{
    "topic":      "patientSync",
    "tlClientId": tlid,
    "status":     "complete",
    "syncType":   syncType,
    "count":      len(patients),
    "duration":   time.Since(startTime).String(),
}, "患者数据同步完成")

// 同步错误
std.LogErrorLnAndFields(logrus.Fields{
    "topic":      "patientSync",
    "tlClientId": tlid,
    "syncType":   syncType,
    "query":      queryString,
}, "患者数据同步失败", err)
```

### 7.2 医生信息同步

```go
// 同步开始
std.LogInfoAndFields(logrus.Fields{
    "topic":      "doctorSync",
    "tlClientId": tlid,
    "status":     "start",
}, "开始同步医生数据")

// 同步完成
std.LogInfoAndFields(logrus.Fields{
    "topic":      "doctorSync",
    "tlClientId": tlid,
    "status":     "complete",
    "count":      len(doctors),
}, "医生数据同步完成")
```

### 7.3 Redis操作

```go
// Redis写入
std.LogInfoAndFields(logrus.Fields{
    "topic":      "redisWrite",
    "tlClientId": tlid,
    "status":     status,
    "redisKey":   "InPatient-" + tid,
    "count":      len(RedisMap1),
}, "写入Redis缓存")

// Redis错误
std.LogErrorLnAndFields(logrus.Fields{
    "topic":      "redisWrite",
    "tlClientId": tlid,
    "redisKey":   "InPatient-" + tid,
}, "Redis写入失败", err)
```

## 8. 日志监控与告警

### 8.1 监控指标

- **错误率监控**：ERROR级别日志数量/总请求数
- **异常检测**：FATAL级别日志立即告警
- **性能监控**：关键操作耗时超阈值告警
- **同步状态**：数据同步失败或数据量异常

### 8.2 告警渠道

- **即时告警**：短信、钉钉
- **常规告警**：邮件
- **告警升级**：未处理告警自动升级通知主管

### 8.3 告警阈值

| 指标 | 告警阈值 | 告警级别 |
|------|----------|----------|
| ERROR日志数 | >10/分钟 | 普通 |
| FATAL日志 | 任何一条 | 严重 |
| 同步失败 | 连续3次 | 严重 |
| 数据量异常 | 波动>50% | 普通 |

## 9. 日志分析与使用

### 9.1 常用查询场景

- **问题排查**：根据时间段+错误关键字
- **性能分析**：根据操作类型+耗时
- **安全审计**：根据用户ID+操作类型
- **业务统计**：根据业务指标聚合分析

### 9.2 Kibana面板

建议创建以下Kibana面板：
- 系统健康状态面板
- 数据同步状态面板
- 错误分布面板
- 性能监控面板

## 10. 规范执行与评审

- 新代码提交前进行日志规范审查
- 定期对日志质量进行评估
- 根据实际使用情况持续优化日志规范

## 附录：日志初始化代码

```go
// 初始化日志系统
func initLogging() {
    // 初始化标准日志
    std.InitLog(Config.Log)
    
    // 初始化Elasticsearch日志
    std.InitWriteToElasticSearch(
        Config.Log,
        Config.ElasticSearch,
        "ivankadedicated-api_robot",
        0,
    )
    
    log.Println("日志系统初始化完成")
}
``` 