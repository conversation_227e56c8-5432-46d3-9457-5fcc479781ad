module gitlab.itingluo.com/backend/ivankabasepush

go 1.23.2

toolchain go1.24.4

require (
	github.com/go-sql-driver/mysql v1.6.0
	github.com/redis/go-redis/v9 v9.5.1
	github.com/sirupsen/logrus v1.9.3
	gitlab.itingluo.com/backend/ivankastd v0.0.0
	golang.org/x/text v0.18.0
	gorm.io/gorm v1.25.7-0.20240204074919-46816ad31dde
)

require (
	github.com/go-logfmt/logfmt v0.5.0 // indirect
	github.com/jinzhu/gorm v1.9.16 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/kr/pretty v0.3.0 // indirect
	github.com/mattn/go-sqlite3 v1.14.22 // indirect
	github.com/microsoft/go-mssqldb v1.7.2 // indirect
	github.com/nsqio/go-nsq v1.1.0 // indirect
	gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c // indirect
	gorm.io/driver/sqlite v1.5.7 // indirect
	gorm.io/driver/sqlserver v1.5.4 // indirect
)

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/PuerkitoBio/goquery v1.5.1 // indirect
	github.com/andybalholm/cascadia v1.1.0 // indirect
	github.com/aw16com/rate v0.0.0-20170602052110-062ff4817e93 // indirect
	github.com/aymerick/douceur v0.2.0 // indirect
	github.com/certifi/gocertifi v0.0.0-20200922220541-2c3bb06c6054 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/elastic/elastic-transport-go/v8 v8.3.0 // indirect
	github.com/elastic/go-elasticsearch/v8 v8.11.1 // indirect
	github.com/evalphobia/logrus_sentry v0.8.2 // indirect
	github.com/getsentry/raven-go v0.2.0 // indirect
	github.com/go-redis/redis v6.15.9+incompatible // indirect
	github.com/gocql/gocql v1.0.0 // indirect
	github.com/godror/godror v0.28.0
	github.com/golang-jwt/jwt/v4 v4.4.1 // indirect
	github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9 // indirect
	github.com/golang-sql/sqlexp v0.1.0 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/css v1.0.0 // indirect
	github.com/hailocab/go-hostpool v0.0.0-20160125115350-e80d13ce29ed // indirect
	github.com/jinzhu/configor v1.2.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/lunny/html2md v0.0.0-20181018071239-7d234de44546 // indirect
	github.com/micro/go-micro v1.18.0 // indirect
	github.com/microcosm-cc/bluemonday v1.0.18 // indirect
	github.com/moul/http2curl v1.0.0 // indirect
	github.com/mozillazg/go-pinyin v0.19.0 // indirect
	github.com/nxadm/tail v1.4.11 // indirect
	github.com/parnurzeal/gorequest v0.3.0 // indirect
	github.com/pborman/uuid v1.2.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/russross/blackfriday v1.6.0 // indirect
	github.com/thda/tds v0.1.7
	gitlab.itingluo.com/backend/ivankasecret v0.0.0 // indirect
	golang.org/x/crypto v0.27.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	google.golang.org/genproto v0.0.0-20200825200019-8632dd797987 // indirect
	google.golang.org/grpc v1.48.0 // indirect
	//google.golang.org/protobuf v1.26.0 // indirect
	//golang.org/x/tools v0.0.0-20190802003818-e9bb7d36c060 //indirect
	//github.com/mattn/go-runewidth v0.0.4 // indirect
	//github.com/xo/tblfmt v0.0.0-20190609041254-28c54ec42ce8 // indirect
	//github.com/chzyer/test v0.0.0-20180213035817-a1ea475d72b1 // indirect
	//github.com/chzyer/readline v0.0.0-20180603132655-2972be24d48e // indirect
	//github.com/chzyer/logex v1.1.10 // indirect
	//github.com/go-logfmt/logfmt v0.6.0 // indirect
	//github.com/godror/godror v0.29.0 // indirect
	//github.com/godror/knownpb v0.1.1 // indirect
	//golang.org/x/exp v0.0.0-20240318143956-a85f2c67cd81 // indirect
	//github.com/axgle/mahonia v0.0.0-20180208002826-3358181d7394 // indirect
	//github.com/saintfish/chardet v0.0.0-20230101081208-5e3ef4b5456d // indirect
	google.golang.org/protobuf v1.30.0 //indirect
	//google.golang.org/protobuf v1.30.0 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/redis.v5 v5.2.9 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	qiniupkg.com/x v1.11.5 // indirect
)

replace qiniupkg.com/x v1.11.5 => qiniupkg.com/x v1.7.0

replace gitlab.itingluo.com/backend/ivankastd v0.0.0 => ../ivankastd

replace gitlab.itingluo.com/backend/ivankasecret v0.0.0 => ../ivankasecret

replace gitlab.itingluo.com/backend/ivankaprotocol v0.0.0 => ../maskprotocol

replace google.golang.org/grpc v1.36.0 => google.golang.org/grpc v1.26.0

replace gitlab.wallstcn.com/micro/go-plugins v0.0.0 => ../micro/go-plugins

replace gitlab.wallstcn.com/micro/istio-plugins v0.0.0 => ../micro/istio-plugins

replace github.com/russross/blackfriday v2.0.0+incompatible => github.com/russross/blackfriday v1.5.2

replace github.com/coreos/bbolt v1.3.4 => go.etcd.io/bbolt v1.3.4
