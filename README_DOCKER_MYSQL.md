# Docker MySQL 环境配置指南

## 快速启动

### 1. 启动 MySQL 环境
```bash
# 给启动脚本执行权限（如果还没有的话）
chmod +x start-mysql.sh

# 启动 MySQL Docker 容器
./start-mysql.sh
```

### 2. 测试连接和数据结构
```bash
# 测试 MySQL 连接
go run test_mysql_connection.go

# 测试数据结构和示例数据
go run test_mysql_structure.go
```

### 3. 运行程序
```bash
# 运行主程序
go run main.go
```

## 环境配置详情

### Docker 服务
- **MySQL 8.0**: 端口 3306
- **phpMyAdmin** (可选): 端口 8080

### 数据库配置
- **数据库名**: collectloop_db
- **用户名**: hisbridge
- **密码**: hisbridge123
- **主机**: localhost
- **端口**: 3306

### 数据库结构

#### 主要表/视图:
1. **spt_department** - 科室信息表
2. **spt_user** - 用户信息表
3. **spt_patient** - 患者信息表
4. **view_spt_department** - 科室信息视图
5. **view_spt_user** - 用户信息视图
6. **view_spt_patient** - 患者信息视图

#### 数据结构特点:
- 完全按照 `数据视图结构说明.md` 规范设计
- 支持UTF8MB4字符集，避免中文乱码
- 包含完整的科室、用户、患者关联关系
- 支持护理级别、入院诊断等详细信息

#### 示例数据:
- 7个科室记录（内科、外科、骨科、妇科、儿科等）
- 8个用户记录（5个医生，3个护士）
- 11个患者记录（6个在院，3个出院，2个最近入院）

## 常用命令

### Docker 管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker logs collectloop_mysql

# 进入 MySQL 容器
docker exec -it collectloop_mysql mysql -uhisbridge -phisbridge123 collectloop_db

# 重启服务
docker-compose restart mysql
```

### 数据库操作
```bash
# 连接数据库
mysql -h localhost -P 3306 -u hisbridge -p collectloop_db

# 查看科室数据
SELECT * FROM view_spt_department;

# 查看用户数据
SELECT user_name, name, role_id, inpatient_ward FROM view_spt_user;

# 查看患者数据
SELECT name, hospitalization_no, status, inpatient_ward FROM view_spt_patient LIMIT 5;

# 查看医生数据
SELECT user_name, name, inpatient_ward, mobile FROM view_spt_user WHERE role_id = 1;

# 查看在院患者
SELECT name, hospitalization_no, inhospital_time, doctor_id FROM view_spt_patient WHERE status = 1;

# 查看患者统计
SELECT inpatient_ward, COUNT(*) as count FROM view_spt_patient WHERE status = 1 GROUP BY inpatient_ward;
```

## 文件结构

```
collectloop/
├── docker-compose.yml          # Docker 编排文件
├── start-mysql.sh             # 启动脚本
├── test_mysql_connection.go   # 连接测试脚本
├── test_mysql_structure.go   # 数据结构测试脚本
├── mysql-init/                # MySQL 初始化脚本
│   ├── 01-create-tables.sql   # 创建表和视图
│   └── 02-insert-sample-data.sql # 插入示例数据
├── conf/
│   └── config.json           # 应用配置文件
└── README_DOCKER_MYSQL.md   # 本文件
```

## 故障排除

### MySQL 启动失败
```bash
# 查看详细日志
docker logs collectloop_mysql

# 检查端口占用
lsof -i :3306

# 重新创建容器
docker-compose down -v
docker-compose up -d mysql
```

### 连接被拒绝
1. 确认 MySQL 容器正在运行: `docker ps`
2. 检查配置文件中的连接信息
3. 确认防火墙设置

### 数据丢失
- 数据存储在 Docker volume `mysql_data` 中
- 除非使用 `docker-compose down -v`，否则数据会持久保存

## phpMyAdmin 访问

如果启动了 phpMyAdmin:
- 访问地址: http://localhost:8080
- 用户名: hisbridge  
- 密码: hisbridge123

## 生产环境注意事项

1. **修改默认密码**: 在生产环境中修改默认密码
2. **数据备份**: 定期备份数据库
3. **网络安全**: 配置防火墙规则
4. **资源限制**: 根据需要调整 Docker 资源限制
