package cache

import (
	"context"
	"time"

	redis "github.com/redis/go-redis/v9"
	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit"
)

var (
	redisClient *redis.Client
)

func InitRedis(config std.ConfigRedis) {
	redisClient = toolkit.InitRedisv9(config)
}

func R() *redis.Client {
	return redisClient
}

func Acquire(key, val string, expire time.Duration) bool {
	return redisClient.SetNX(context.Background(), key, val, expire).Val()
}

func Set(key, value string, timeOut int) {
	if redisClient == nil || key == "" || value == "" {
		return
	}
	redisClient.Set(context.Background(), key, value, time.Duration(timeOut)*time.Second)
}

func ZAdd(key string, score float64, member interface{}) int64 {
	if redisClient == nil || key == "" || member == nil {
		return 0
	}
	return redisClient.ZAdd(context.Background(), key, redis.Z{Score: score, Member: member}).Val()
}

func ZRangeByScore(key, min, max string, offset, count int64) []string {
	if redisClient == nil || key == "" || min == "" || max == "" {
		return nil
	}
	return redisClient.ZRangeByScore(context.Background(), key, &redis.ZRangeBy{Min: min, Max: max, Offset: offset, Count: count}).Val()
}

func ZRemRangeByScore(key, min, max string) int64 {
	if redisClient == nil || key == "" || min == "" || max == "" {
		return 0
	}
	return redisClient.ZRemRangeByScore(context.Background(), key, min, max).Val()
}

func ZCard(key string) int64 {
	if redisClient == nil || key == "" {
		return 0
	}
	return redisClient.ZCard(context.Background(), key).Val()
}

func Get(key string) string {
	if redisClient == nil || key == "" {
		return ""
	}
	return redisClient.Get(context.Background(), key).Val()
}

func HScan(key string, cursor uint64, match string, count int64) string {
	if redisClient == nil || key == "" {
		return ""
	}
	return HScan(key, cursor, match, count)
}
