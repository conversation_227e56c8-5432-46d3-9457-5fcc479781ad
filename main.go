package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"reflect"
	"strconv"
	"time"

	c "gitlab.itingluo.com/backend/ivankabasepush/cache"
	"gitlab.itingluo.com/backend/ivankabasepush/common"
	"gitlab.itingluo.com/backend/ivankabasepush/dao"
	"gitlab.itingluo.com/backend/ivankabasepush/models"
	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit"
)

func init() {
	s, err := StartProject()
	if err != nil {
		fmt.Println("[error]:", err)
		os.Exit(1)
		return
	}
	models.Hospital = s
}

func main() {

	common.InitModel("./conf/ivankabasepush.ivtest.yaml")
	Config := common.GlobalConf
	var DbName string
	var TlClientName string

	Config.Mssql.Username = models.Hospital.C.MssqlUsername
	Config.Mssql.Password = models.Hospital.C.MssqlPassword
	Config.Mssql.Host = models.Hospital.C.MssqlHost
	Config.Mssql.Port, _ = strconv.Atoi(models.Hospital.C.MssqlPort)
	Config.Mssql.DBName = models.Hospital.C.MssqlDBName
	Config.Redis.Host = models.Hospital.C.RedisHost
	Config.Redis.Port, _ = strconv.Atoi(models.Hospital.C.RedisPort)
	Config.Redis.Auth = models.Hospital.C.RedisAuth
	Config.Redis.IdleTimeout, _ = strconv.Atoi(models.Hospital.C.RedisIdleTimeout)
	Config.Redis.DB, _ = strconv.Atoi(models.Hospital.C.RedisDB)
	DbName = models.Hospital.C.DbName
	TlClientName = models.Hospital.C.TlClientName
	tlid := strconv.Itoa(models.Hospital.C.TlClientId)
	log.Println("sql:", DbName)
	log.Println("sqlInfo:", Config.Mssql)
	log.Println("sqlRedis:", Config.Redis)
	log.Println("TlClientName:", TlClientName)
	log.Println("tlId:", tlid)
	log.Println("TODB_SWITCH:", models.Hospital.C.ToDbSwitch)

	std.InitLog(Config.Log) // 日志看板，日志看板。
	std.InitWriteToElasticSearch(Config.Log, Config.ElasticSearch, "ivankadedicated-api_robot", 0)
	c.InitRedis(Config.Redis)
	switch DbName {
	case "sqlserver":
		models.InitModel(Config.Mssql)
		defer models.CloseDB() //1
		fmt.Println("start sqlserver")
	case "oracle":
		models.InitOracleModel(Config.Mssql)
		defer models.CloseOracleDB()
		fmt.Println("start oracle")
	case "sybase":
		models.InitSyabseModel(Config.Mssql)
		defer models.CloseSybaseDB()
		fmt.Println("start sybase")
	case "mysql":
		models.InitMysqlModel(Config.Mssql)
		defer models.CloseMysqlDB()
		fmt.Println("start mysql")
	}
	dao.GetAfDoctorList(tlid, DbName, TlClientName)
	go Heartbeat()
	go writeInPatientByRedis(DbName)
	go writeOutPatientByRedis(DbName)
	go writeUpPatientByRedis(DbName)
	go writeDoctorByRedis(DbName, TlClientName)

	// 添加Redis到数据库的同步任务
	if models.Hospital.C.ToDbSwitch == "true" {
		go redisToDbSync(DbName)
	}

	for {
		time.Sleep(time.Minute * 3)
	}
}

func writeInPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*1, "In", DbName)
}
func writeOutPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*5, "Out", DbName)
}
func writeUpPatientByRedis(DbName string) {
	dao.TickInPatientByRedis(time.Minute*2, "Up", DbName)
}
func writeDoctorByRedis(DbName string, TlClientName string) {
	dao.TickDoctorByRedis(time.Hour*12, "Doctor", DbName, TlClientName)
}

func redisToDbSync(DbName string) {
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId)
	ticker := time.NewTicker(time.Minute * 5) // 每5分钟同步一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			log.Println("Starting Redis to Database sync...")

			// 同步入院患者数据
			dao.RedisToDatabase(tlClientId, "In", DbName)

			// 同步出院患者数据
			dao.RedisToDatabase(tlClientId, "Out", DbName)

			// 同步在院患者数据
			dao.RedisToDatabase(tlClientId, "Up", DbName)

			log.Println("Redis to Database sync completed")
		}
	}
}

func Heartbeat() {
	h := toolkit.NewHeartbeat("collectloop", models.Hospital.C.TlClientId, time.Minute*5, os.Getenv("VERSION"))
	h.Start()
}

func StartProject() (*models.HospitalInfo, error) {
	config := &models.HospitalInfo{}
	//configPath := "/data/docker-compose/eli/conf/config.json"
	configPath := "./conf/config.json"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return config, fmt.Errorf("config file does not exist: %s", configPath)
	}
	fileContent, err := ioutil.ReadFile(configPath)
	if err != nil {
		return config, fmt.Errorf("error reading config file: %w", err)
	}
	if err := json.Unmarshal(fileContent, config); err != nil {
		return config, fmt.Errorf("error unmarshaling config file: %w", err)
	}
	v := reflect.ValueOf(config.C).Elem()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := v.Type().Field(i).Name
		// 跳过 MssqlPassword 的检查
		if fieldName == "MssqlPassword" {
			continue
		}
		if field.Kind() == reflect.String && field.String() == "" {
			return config, fmt.Errorf("config field %s is empty", fieldName)
		}
		if field.Kind() == reflect.Int && field.Int() == 0 {
			return config, fmt.Errorf("config field %s is empty", fieldName)
		}
	}
	return config, nil
}
