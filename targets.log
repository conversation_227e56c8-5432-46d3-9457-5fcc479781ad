make all --print-data-base --no-builtin-variables --no-builtin-rules --question
# GNU Make 3.81
# Copyright (C) 2006  Free Software Foundation, Inc.
# This is free software; see the source for copying conditions.
# There is NO warranty; not even for MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

# This program built for i386-apple-darwin11.3.0

# Make data base, printed on Thu Jul  3 09:18:28 2025

# Variables

# automatic
<D = $(patsubst %/,%,$(dir $<))
# automatic
?F = $(notdir $?)
# environment
VSCODE_CRASH_REPORTER_PROCESS_TYPE = extensionHost
# environment
VSCODE_PROCESS_TITLE = extension-host [4-4]
# environment
GRADLE_HOME = /Applications/AndroidStudio.app/Contents/gradle/gradle-5.1.1⁩
# environment
WEBSTORM_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/webstorm.vmoptions
# automatic
@D = $(patsubst %/,%,$(dir $@))
# automatic
@F = $(notdir $@)
# makefile
CURDIR := /Users/<USER>/2025/xy/git/collectloop
# makefile
SHELL = /bin/sh
# environment
APPCODE_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/appcode.vmoptions
# environment
VSCODE_NLS_CONFIG = {"userLocale":"zh-cn","osLocale":"zh-cn","resolvedLanguage":"zh-cn","defaultMessagesFile":"/Applications/Cursor.app/Contents/Resources/app/out/nls.messages.json","languagePack":{"translationsConfigFile":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","messagesFile":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/5b19bac7a947f54e4caa3eb7e4c5fbf832389850/nls.messages.json","corruptMarkerFile":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"},"locale":"zh-cn","availableLanguages":{"*":"zh-cn"},"_languagePackId":"6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_languagePackSupport":true,"_translationsConfigFile":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/tcf.json","_cacheRoot":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn","_resolvedLanguagePackCoreLocation":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/5b19bac7a947f54e4caa3eb7e4c5fbf832389850","_corruptedFile":"/Users/<USER>/Library/Application Support/Cursor/clp/6d6cd612ec0ae3cd32737a6f6b7ad966.zh-cn/corrupted.info"}
# environment
_ = /usr/bin/make
# automatic
?D = $(patsubst %/,%,$(dir $?))
# makefile (from `Makefile', line 1)
MAKEFILE_LIST :=  Makefile
# environment
__CFBundleIdentifier = com.todesktop.230313mzl4w4u92
# environment
WEBIDE_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/webide.vmoptions
# environment
VSCODE_CWD = /
# environment
GATEWAY_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/gateway.vmoptions
# environment
CONFIGOR_ENV = local
# environment
PATH = /Library/Frameworks/Python.framework/Versions/3.10/bin:/Applications/MxSrvs/bin/composer/bin:/Applications/MxSrvs/bin/php/bin:/Users/<USER>/Library/flutter/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/share/dotnet:/Library/Apple/usr/bin:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Applications/Xamarin Workbooks.app/Contents/SharedSupport/path-bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/build-tools/26.1.1:/Users/<USER>/android-sdk-macosx/platform-tools/:/Users/<USER>/gradle/bin:/Users/<USER>/vcpkg:/Users/<USER>/mygo/go/bin:/bin:/bin
# environment
PHPSTORM_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/phpstorm.vmoptions
# environment
GOPATH = /Users/<USER>/mygo/go
# environment
GOLAND_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/goland.vmoptions
# environment
ELECTRON_RUN_AS_NODE = 1
# environment
ANDROID_HOME = /Users/<USER>/Library/Android/sdk
# environment
JETBRAINSCLIENT_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/jetbrainsclient.vmoptions
# default
.FEATURES := target-specific order-only second-expansion else-if archives jobserver check-symlink
# environment
IDEA_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/idea.vmoptions
# environment
DATASPELL_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/dataspell.vmoptions
# environment
SSH_AUTH_SOCK = /private/tmp/com.apple.launchd.TYpQM2YZIW/Listeners
# automatic
%F = $(notdir $%)
# environment
GOBIN = /Users/<USER>/mygo/go/bin
# environment
PWD = /Users/<USER>/2025/xy/git/collectloop
# environment
JETBRAINS_CLIENT_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/jetbrains_client.vmoptions
# environment
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
# environment
MANPATH = /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/share/man:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/share/man:/Applications/Xcode.app/Contents/Developer/usr/share/man:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/man:
# environment
HOME = /Users/<USER>
# automatic
+D = $(patsubst %/,%,$(dir $+))
# environment
RIDER_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/rider.vmoptions
# default
MAKEFILEPATH := /Applications/Xcode.app/Contents/Developer/Makefiles
# environment
VSCODE_CODE_CACHE_PATH = /Users/<USER>/Library/Application Support/Cursor/CachedData/5b19bac7a947f54e4caa3eb7e4c5fbf832389850
# environment
LOGNAME = zhaochenyu
# environment
VSCODE_HANDLES_UNCAUGHT_ERRORS = true
# automatic
^D = $(patsubst %/,%,$(dir $^))
# environment
XPC_FLAGS = 0x0
# default
MAKE = $(MAKE_COMMAND)
# default
MAKECMDGOALS := all
# environment
PUB_HOSTED_URL = https://pub.flutter-io.cn
# environment
VSCODE_ESM_ENTRYPOINT = vs/workbench/api/node/extensionHostProcess
# default
MAKE_VERSION := 3.81
# environment
USER = zhaochenyu
# makefile
.DEFAULT_GOAL := list
# automatic
%D = $(patsubst %/,%,$(dir $%))
# environment
CURSOR_TRACE_ID = 0958087f72234584892db32312c7742c
# default
MAKE_COMMAND := /Applications/Xcode.app/Contents/Developer/usr/bin/make
# default
.VARIABLES := 
# environment
TMPDIR = /var/folders/sp/nmbkhh9j7qz6g07fj7mtb0kc0000gn/T/
# automatic
*F = $(notdir $*)
# environment
VSCODE_IPC_HOOK = /Users/<USER>/Library/Application Support/Cursor/1.1.-main.sock
# environment
MallocNanoZone = 0
# makefile
MAKEFLAGS = Rrqp
# environment
MFLAGS = -Rrqp
# automatic
*D = $(patsubst %/,%,$(dir $*))
# environment
XPC_SERVICE_NAME = application.com.todesktop.230313mzl4w4u92.4788339039.4788339045
# environment
FLUTTER_STORAGE_BASE_URL = https://storage.flutter-io.cn
# environment
DATAGRIP_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/datagrip.vmoptions
# automatic
+F = $(notdir $+)
# environment
COMPOSER_HOME = /Applications/MxSrvs/bin/composer
# environment
COMPOSER_CACHE_DIR = /Applications/MxSrvs/cache/composer
# environment
RUBYMINE_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/rubymine.vmoptions
# environment
__CF_USER_TEXT_ENCODING = 0x1F5:0x19:0x34
# environment
PYCHARM_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/pycharm.vmoptions
# environment
COMMAND_MODE = unix2003
# environment
VCPKG_ROOT = /Users/<USER>/vcpkg
# default
MAKEFILES := 
# automatic
<F = $(notdir $<)
# environment
LC_ALL = C
# automatic
^F = $(notdir $^)
# environment
GEMINI_API_KEY = AIzaSyBYYjH4nkQw5eOSbCS-c4Na9HPXlAo56Ww
# default
SUFFIXES := 
# default
.INCLUDE_DIRS = /usr/local/include
# environment
MAKELEVEL := 0
# environment
SHLVL = 2
# environment
TL_ClIENT_ID = 1000000000222
# environment
CLION_VM_OPTIONS = /Users/<USER>/idea/ja-netfilter-all/vmoptions/clion.vmoptions
# environment
LANG = C
# environment
VSCODE_PID = 1144
# variable set hash-table stats:
# Load=88/1024=9%, Rehash=0, Collisions=5/112=4%

# Pattern-specific Variable Values

# No pattern-specific variable values.

# Directories

# . (device 16777220, inode 4788125782): 27 files, no impossibilities.

# 27 files, no impossibilities in 1 directories.

# Implicit Rules

# No implicit rules.

# Files

# Not a target:
all:
#  Command-line target.
 
make: *** No rule to make target `all'.  Stop.

#  Implicit rule search has been done.
#  File does not exist.
#  File has not been updated.
# variable set hash-table stats:
# Load=0/32=0%, Rehash=0, Collisions=0/0=0%

.PHONY: list sync-codes build deps test
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

deps:
#  Phony target (prerequisite of .PHONY).
#  Implicit rule search has not been done.
#  File does not exist.
#  File has not been updated.
#  commands to execute (from `Makefile', line 22):
	mkdir -p /tmp/govendor_src/src/gitlab.wallstcn.com/wscnbackend
	@if [ "$(CI_COMMIT_REF_NAME)" = "master" ]; then\
	echo "checkout ivankastd:master";\
	git clone -b release ssh://**********************:8082/backend/ivankastd.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd;\
	git clone -b main ssh://***********************:8082/backend/ivankasecret.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret;\
	git clone ssh://**********************:8082/backend/maskprotocol.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol;\
	git clone ssh://**********************:8082/backend/micro.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/micro;\
	else\
	echo "checkout ivankastd:sit";\
	git clone -b release ssh://**********************:8082/backend/ivankastd.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd;\
	git clone -b main ssh://***********************:8082/backend/ivankasecret.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret;\
	git clone ssh://**********************:8082/backend/maskprotocol.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol;\
	git clone ssh://**********************:8082/backend/micro.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/micro;\
	fi
	rm -rf "/builds/backend/ivankastd/"
	rm -rf "/builds/backend/ivankasecret/"
	rm -rf "/builds/backend/maskprotocol/"
	rm -rf "/builds/backend/micro/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd" "/builds/backend/ivankastd/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol" "/builds/backend/maskprotocol/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret" "/builds/backend/ivankasecret/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/micro" "/builds/backend/micro/"
	

# Not a target:
.SUFFIXES:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

# Not a target:
Makefile:
#  Implicit rule search has been done.
#  Last modified 2025-07-02 15:26:30
#  File has been updated.
#  Successfully updated.
# variable set hash-table stats:
# Load=0/32=0%, Rehash=0, Collisions=0/0=0%

test:
#  Phony target (prerequisite of .PHONY).
#  Implicit rule search has not been done.
#  File does not exist.
#  File has not been updated.
#  commands to execute (from `Makefile', line 48):
	go test
	

build:
#  Phony target (prerequisite of .PHONY).
#  Implicit rule search has not been done.
#  File does not exist.
#  File has not been updated.
#  commands to execute (from `Makefile', line 12):
	PATH=/tmp/govendor/bin:$(PATH)
	GOPATH=/tmp/govendor/:$(GOPATH)
	# 运行 go mod tidy 来整理依赖项
	go mod tidy
	# 编译项目
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -ldflags '-w' -o server ./main.go
	

sync-codes:
#  Phony target (prerequisite of .PHONY).
#  Implicit rule search has not been done.
#  File does not exist.
#  File has not been updated.
#  commands to execute (from `Makefile', line 9):
	git pull
	

# Not a target:
.DEFAULT:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

list:
#  Phony target (prerequisite of .PHONY).
#  Implicit rule search has not been done.
#  File does not exist.
#  File has not been updated.
#  commands to execute (from `Makefile', line 4):
	@echo "sync-codes: 同步代码"
	@echo "build: 编译可执行文件"
	@echo "test: 测试"
	

# files hash-table stats:
# Load=10/1024=1%, Rehash=0, Collisions=0/27=0%
# VPATH Search Paths

# No `vpath' search paths.

# No general (`VPATH' variable) search path.

# # of strings in strcache: 1
# # of strcache buffers: 1
# strcache size: total = 4096 / max = 4096 / min = 4096 / avg = 4096
# strcache free: total = 4087 / max = 4087 / min = 4087 / avg = 4087

# Finished Make data base on Thu Jul  3 09:18:28 2025

 
