package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 测试MySQL连接和数据结构
	username := "hisbridge"
	password := "hisbridge123"
	host := "localhost"
	port := "3306"
	dbName := "collectloop_db"

	// MySQL DSN格式
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, dbName)

	fmt.Printf("🔗 连接MySQL: %s@%s:%s/%s\n", username, host, port, dbName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("❌ 打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	err = db.<PERSON>()
	if err != nil {
		log.Fatalf("❌ 连接数据库失败: %v", err)
	}

	fmt.Println("✅ MySQL连接成功!")

	// 检查视图是否存在
	views := []string{"view_spt_department", "view_spt_user", "view_spt_patient"}
	for _, view := range views {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM information_schema.views WHERE table_schema = '%s' AND table_name = '%s'", dbName, view)).Scan(&count)
		if err != nil {
			log.Printf("❌ 检查视图 %s 失败: %v", view, err)
		} else if count > 0 {
			fmt.Printf("✅ 视图 %s 存在\n", view)
		} else {
			fmt.Printf("❌ 视图 %s 不存在\n", view)
		}
	}

	// 测试科室数据
	fmt.Println("\n📊 科室数据:")
	rows, err := db.Query("SELECT department_id, code, name FROM view_spt_department LIMIT 5")
	if err != nil {
		log.Printf("❌ 查询科室数据失败: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var deptId int
			var code, name string
			rows.Scan(&deptId, &code, &name)
			fmt.Printf("  科室ID: %d, 编码: %s, 名称: %s\n", deptId, code, name)
		}
	}

	// 测试用户数据
	fmt.Println("\n👥 用户数据:")
	rows, err = db.Query("SELECT user_name, name, role_id, mobile FROM view_spt_user LIMIT 5")
	if err != nil {
		log.Printf("❌ 查询用户数据失败: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var userName, name, mobile string
			var roleId int
			rows.Scan(&userName, &name, &roleId, &mobile)
			roleStr := "其他"
			if roleId == 1 {
				roleStr = "医生"
			} else if roleId == 2 {
				roleStr = "护士"
			}
			fmt.Printf("  用户: %s, 姓名: %s, 角色: %s, 手机: %s\n", userName, name, roleStr, mobile)
		}
	}

	// 测试患者数据
	fmt.Println("\n🏥 患者数据:")
	rows, err = db.Query("SELECT name, hospitalization_no, status, inpatient_ward, doctor_id FROM view_spt_patient LIMIT 5")
	if err != nil {
		log.Printf("❌ 查询患者数据失败: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var name, hospNo, ward, doctorId string
			var status int
			rows.Scan(&name, &hospNo, &status, &ward, &doctorId)
			statusStr := "未知"
			if status == 1 {
				statusStr = "在院"
			} else if status == 2 {
				statusStr = "出院"
			}
			fmt.Printf("  患者: %s, 住院号: %s, 状态: %s, 病区: %s, 医生: %s\n", name, hospNo, statusStr, ward, doctorId)
		}
	}

	// 测试时间戳查询（模拟程序中的查询）
	fmt.Println("\n⏰ 测试时间戳查询:")
	query := "SELECT name, " +
		"UNIX_TIMESTAMP(inhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') AS inhospital_time, " +
		"CASE WHEN outhospital_time IS NULL THEN 0 ELSE UNIX_TIMESTAMP(outhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') END AS outhospital_time " +
		"FROM view_spt_patient WHERE status = 1 LIMIT 3"
	
	rows, err = db.Query(query)
	if err != nil {
		log.Printf("❌ 时间戳查询失败: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var name string
			var inTime, outTime int64
			rows.Scan(&name, &inTime, &outTime)
			fmt.Printf("  患者: %s, 入院时间戳: %d, 出院时间戳: %d\n", name, inTime, outTime)
		}
	}

	fmt.Println("\n🎉 数据结构测试完成!")
}
