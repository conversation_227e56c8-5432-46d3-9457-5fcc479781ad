# Code generated by guidedog. DO NOT EDIT.
image: ccr.ccs.tencentyun.com/itingluo/build-base:latest
stages:
- test
- build
- release
- deployment

variables:
  DOCKER_DRIVER: overlay
  SERVICE_NAME: collectloop
  NAMESPACE: default
  IMAGE_NAME: ccr.ccs.tencentyun.com/itingluo/collectloop
  CONTAINER_TEST_IMAGE: $IMAGE_NAME:$CI_BUILD_REF_NAME
  CONTAINER_RELEASE_IMAGE: $IMAGE_NAME:latest
  CONTAINER_TAG_IMAGE: $IMAGE_NAME:$CI_COMMIT_TAG
  CONTAINER_SHA_IMAGE: $IMAGE_NAME:$CI_COMMIT_SHORT_SHA


build:
  image: ccr.ccs.tencentyun.com/itingluo/golang:1.23.2
  stage: build
  before_script:
    # Install ssh-agent if not already installed, it is required by Docker.
    # (change apt-get to yum if you use a CentOS-based image)
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    # Run ssh-agent (inside the build environment)
    - eval $(ssh-agent -s)
    # Add the SSH key stored in SSH_PRIVATE_KEY variable to the agent store
    - ssh-add <(echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY")
    # For Docker builds disable host key checking. Be aware that by adding that
    # you are suspectible to man-in-the-middle attacks.
    # WARNING: Use this only with the Docker executor, if you use it with shell
    # you will overwrite your user's SSH config.
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - make deps
    - export PATH=/tmp/govendor/bin:$PATH
    - export GOPATH=/tmp/govendor/:$GOPATH
    - go env -w GOPROXY=https://goproxy.cn,direct
    - make build
    - mkdir -p binaries
    - mv ./server binaries/
  artifacts:
    paths:
      - binaries/
    expire_in: 1h
  only:
    - master
    - sqlserver
    - sqlserver2024
    - sqlserver-sybase-oracle
    - sqlserver-sybase-oracle-all
   
release:sit:
  stage: release
  script:
    - pwd
    - ls binaries
    - cp binaries/server .
    - REVISION=`git rev-parse --short HEAD || echo unknown`
    - docker login --username=songjian@1876069763657134 registry.cn-shanghai.aliyuncs.com -p "$YX_CI_DOCKER_REGISTRY_TOKEN"
    - docker build --tag="$IMAGE_NAME:sit" .
    - docker push "$IMAGE_NAME:sit"
  only:
    - sit
  dependencies:
    - build

release:prod:
  stage: release
  script:
    - pwd
    - ls binaries
    - cp binaries/server .
    - REVISION=`git rev-parse --short=8 HEAD || echo unknown`
    - docker login --username=100001579474 ccr.ccs.tencentyun.com -p "$JY_CI_DOCKER_REGISTRY_TOKEN"
    - docker build --tag="$CONTAINER_SHA_IMAGE" .
    - docker push "$CONTAINER_SHA_IMAGE"
  only:
    - master
    - sqlserver
    - sqlserver2024
    - sqlserver-sybase-oracle
    - sqlserver-sybase-oracle-all
 
  dependencies:
    - build


deployment:prod:
  stage: deployment
  image: ccr.ccs.tencentyun.com/tingluo/ssh-client:v240321
  before_script:
    # Install ssh-agent if not already installed, it is required by Docker.
    # (change apt-get to yum if you use a CentOS-based image)
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    # Run ssh-agent (inside the build environment)
    - eval $(ssh-agent -s)
    # Add the SSH key stored in SSH_PRIVATE_KEY variable to the agent store
    - ssh-add <(echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY")
    # For Docker builds disable host key checking. Be aware that by adding that
    # you are suspectible to man-in-the-middle attacks.
    # WARNING: Use this only with the Docker executor, if you use it with shell
    # you will overwrite your user's SSH config.
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    #- ssh <EMAIL> "kubectl scale --namespace=$NAMESPACE --replicas=0 deployment/$SERVICE_NAME && kubectl scale --namespace=$NAMESPACE --replicas=1 deployment/$SERVICE_NAME"
    - ssh -p 2022 root@************ "docker service update --no-resolve-image --with-registry-auth --image $CONTAINER_SHA_IMAGE collectloop_collectloop"
  only:
    - master


