#!/bin/bash

# MySQL 数据库备份脚本

# 配置
DB_NAME="collectloop_db"
DB_USER="hisbridge"
DB_PASS="hisbridge123"
CONTAINER_NAME="collectloop_mysql"
BACKUP_DIR="./backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/collectloop_db_${DATE}.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

echo "🗄️  开始备份 MySQL 数据库..."

# 检查容器是否运行
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ MySQL 容器未运行，请先启动容器"
    exit 1
fi

# 执行备份
docker exec $CONTAINER_NAME mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "✅ 备份成功: $BACKUP_FILE"
    
    # 显示备份文件大小
    SIZE=$(du -h $BACKUP_FILE | cut -f1)
    echo "📊 备份文件大小: $SIZE"
    
    # 压缩备份文件
    gzip $BACKUP_FILE
    echo "🗜️  备份文件已压缩: ${BACKUP_FILE}.gz"
    
    # 清理旧备份（保留最近7天）
    find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
    echo "🧹 已清理7天前的备份文件"
    
    # 显示所有备份文件
    echo ""
    echo "📁 当前备份文件:"
    ls -lh $BACKUP_DIR/*.sql.gz 2>/dev/null || echo "   无备份文件"
    
else
    echo "❌ 备份失败"
    rm -f $BACKUP_FILE
    exit 1
fi
