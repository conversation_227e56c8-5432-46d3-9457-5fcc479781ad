package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gitlab.itingluo.com/backend/ivankabasepush/cache"
	"gitlab.itingluo.com/backend/ivankabasepush/dao"
	"gitlab.itingluo.com/backend/ivankabasepush/models"
)

func main() {
	// 初始化配置
	config := &models.HospitalInfo{}
	config.C = &models.Config{
		MssqlUsername:    "hisbridge",
		MssqlPassword:    "hisbridge123",
		MssqlHost:        "localhost",
		MssqlPort:        "3306",
		MssqlDBName:      "collectloop_db",
		RedisHost:        "**************",
		RedisPort:        "20272",
		RedisAuth:        "253jianyu001",
		RedisIdleTimeout: "240",
		RedisDB:          "12",
		DbName:           "mysql",
		TlClientName:     "鹰潭贤泽第三医院",
		TlClientId:       1000000000302,
		ToDbSwitch:       "true", // 启用数据库写入
	}
	models.Hospital = config

	// 初始化Redis连接
	redisConfig := struct {
		Host        string
		Port        int
		Auth        string
		IdleTimeout int
		DB          int
	}{
		Host:        config.C.RedisHost,
		Port:        20272,
		Auth:        config.C.RedisAuth,
		IdleTimeout: 240,
		DB:          12,
	}

	// 这里需要适配你的Redis初始化方法
	// cache.InitRedis(redisConfig)

	// 初始化MySQL连接
	mysqlConfig := struct {
		Username string
		Password string
		Host     string
		Port     int
		DBName   string
	}{
		Username: config.C.MssqlUsername,
		Password: config.C.MssqlPassword,
		Host:     config.C.MssqlHost,
		Port:     3306,
		DBName:   config.C.MssqlDBName,
	}

	// 这里需要适配你的MySQL初始化方法
	// models.InitMysqlModel(mysqlConfig)

	fmt.Println("配置信息:")
	fmt.Printf("TODB_SWITCH: %s\n", config.C.ToDbSwitch)
	fmt.Printf("MySQL Host: %s:%s\n", config.C.MssqlHost, config.C.MssqlPort)
	fmt.Printf("Redis Host: %s:%s\n", config.C.RedisHost, config.C.RedisPort)

	// 测试Redis中是否有数据
	testRedisData()

	// 测试数据库写入功能
	testDatabaseWrite()
}

func testRedisData() {
	fmt.Println("\n=== 测试Redis数据 ===")
	
	rs := cache.R()
	if rs == nil {
		fmt.Println("Redis连接失败")
		return
	}

	tlClientId := "1000000000302"
	keys := []string{
		"InPatient-" + tlClientId,
		"OutPatient-" + tlClientId,
		"UpData-" + tlClientId,
	}

	for _, key := range keys {
		// 检查Redis中的数据
		data, err := rs.HGetAll(nil, key).Result()
		if err != nil {
			fmt.Printf("读取Redis键 %s 失败: %v\n", key, err)
			continue
		}
		
		fmt.Printf("Redis键 %s 包含 %d 条记录\n", key, len(data))
		
		// 显示前几条数据的示例
		count := 0
		for k, v := range data {
			if count >= 2 { // 只显示前2条
				break
			}
			
			var patient dao.Patients
			err := json.Unmarshal([]byte(v), &patient)
			if err != nil {
				fmt.Printf("  解析患者数据失败: %v\n", err)
				continue
			}
			
			fmt.Printf("  患者: %s, 住院号: %s, 状态: %d\n", 
				patient.Name, patient.HospitalizationNo, patient.Status)
			count++
		}
	}
}

func testDatabaseWrite() {
	fmt.Println("\n=== 测试数据库写入功能 ===")
	
	if models.Hospital.C.ToDbSwitch != "true" {
		fmt.Println("TODB_SWITCH未启用，跳过数据库写入测试")
		return
	}

	tlClientId := "1000000000302"
	
	// 测试各种状态的数据同步
	statuses := []string{"In", "Out", "Up"}
	
	for _, status := range statuses {
		fmt.Printf("开始同步 %s 状态的数据...\n", status)
		
		start := time.Now()
		dao.RedisToDatabase(tlClientId, status, "mysql")
		duration := time.Since(start)
		
		fmt.Printf("%s 状态数据同步完成，耗时: %v\n", status, duration)
	}
}
