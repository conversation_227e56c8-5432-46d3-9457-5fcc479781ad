package models

import (
	"database/sql"
	"fmt"

	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit"
	"gorm.io/gorm"
)

type HospitalInfo struct {
	C *Config `json:"hospitalinfo"`
}
type Config struct {
	MssqlUsername    string `json:"MSSQL_USERNAME"`
	MssqlPassword    string `json:"MSSQL_PASSWORD"`
	MssqlHost        string `json:"MSSQL_HOST"`
	MssqlPort        string `json:"MSSQL_PORT"`
	MssqlDBName      string `json:"MSSQL_DB_NAME"`
	RedisHost        string `json:"REDIS_HOST"`
	RedisPort        string `json:"REDIS_PORT"`
	RedisAuth        string `json:"REDIS_AUTH"`
	RedisIdleTimeout string `json:"REDIS_IDLE_TIMEOUT"`
	RedisDB          string `json:"REDIS_DB"`
	DbName           string `json:"DB_NAME"`
	TlClientName     string `json:"TL_CLIENT_NAME"`
	TlClientId       int    `json:"TL_CLIENT_ID"`
	ToDbSwitch       string `json:"TODB_SWITCH"`
}

var (
	db       *gorm.DB
	oracleDb *sql.DB
	sybaseDb *sql.DB
	mysqlDb  *sql.DB
	Hospital *HospitalInfo
)

func InitModel(config std.ConfigMssql) {
	db = toolkit.CreateMssqlDB(config)
}
func InitSyabseModel(config std.ConfigMssql) {
	sybaseDb = CreateSybaseDB(config)
}
func SybaseDB() *sql.DB {
	return sybaseDb
}

func DB() *gorm.DB {
	return db
}

func Session() *gorm.DB {
	return db.Begin()
}

func CloseDB() {
	//if db != nil {
	//	db.Close()
	//}
}

func InitOracleModel(config std.ConfigMssql) {
	fmt.Println("111")
	oracleDb = CreateOracleDB(config)
}

func OraclDB() *sql.DB {
	return oracleDb
}

func CloseOracleDB() {
	if oracleDb != nil {
		oracleDb.Close()
	}
}
func CloseSybaseDB() {
	if sybaseDb != nil {
		sybaseDb.Close()
	}
}

func InitMysqlModel(config std.ConfigMssql) {
	mysqlDb = CreateMysqlDB(config)
}

func MysqlDB() *sql.DB {
	return mysqlDb
}

func CloseMysqlDB() {
	if mysqlDb != nil {
		mysqlDb.Close()
	}
}
