package models

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/godror/godror"
	"github.com/thda/tds"
	_ "github.com/thda/tds"
	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit/strutil"
	"golang.org/x/text/encoding/charmap"
)

func CreateSybaseDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = strutil.FromInt64(int64(config.Port))
		dbName   = config.DBName
	)

	tds.RegisterEncoding("cp850", charmap.CodePage850)
	dataSourceName := fmt.Sprintf("tds://%s:%s@%s:%s/%s?language=us_english&charset=cp850", username, password, host, port, dbName)

	db, err := sql.Open("tds", dataSourceName)
	if err != nil {
		log.Fatal(err)
	}
	err = db.Ping()
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("sybase start")

	fmt.Println(db, err)
	db.SetMaxOpenConns(3)
	db.SetMaxIdleConns(3)
	fmt.Println(33333)
	err = db.Ping()
	fmt.Println(4444)
	if err != nil {
		fmt.Println("connectionString err2", err)
		panic(fmt.Sprintf("Err2 failed to connect sybase %s:%s/%s: %s", host, port, dbName, err.Error()))
	}
	fmt.Println(55555)
	return db
}

func CreateOracleDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = strutil.FromInt64(int64(config.Port))
		dbName   = config.DBName
	)

	//"**************:1521/orcl"

	dataSourceName := fmt.Sprintf("user=%s password=%s connectString=%s",
		username, password, host)
	fmt.Println(dataSourceName)
	db, err := sql.Open("godror", dataSourceName)
	fmt.Println(db, err)
	if err != nil {
		fmt.Println("connectionString err", err)
		panic(fmt.Sprintf("Err failed to connect oracle %s: %s", dataSourceName, err.Error()))
	}
	db.SetMaxOpenConns(3)
	db.SetMaxIdleConns(3)
	fmt.Println(33333)
	err = db.Ping()
	fmt.Println(4444)
	if err != nil {
		fmt.Println("connectionString err2", err)
		panic(fmt.Sprintf("Err2 failed to connect oracle %s:%s/%s: %s", host, port, dbName, err.Error()))
	}
	fmt.Println(55555)
	return db
}

func CreateMysqlDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = strutil.FromInt64(int64(config.Port))
		dbName   = config.DBName
	)

	// MySQL DSN format: username:password@tcp(host:port)/dbname
	dataSourceName := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, dbName)

	db, err := sql.Open("mysql", dataSourceName)
	if err != nil {
		log.Fatal(err)
	}

	err = db.Ping()
	if err != nil {
		fmt.Println("MySQL connection error:", err)
		panic(fmt.Sprintf("Failed to connect to MySQL %s:%s/%s: %s", host, port, dbName, err.Error()))
	}

	fmt.Println("MySQL connection successful")
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)

	return db
}
