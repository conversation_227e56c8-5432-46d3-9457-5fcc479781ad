#!/bin/bash

echo "🚀 启动 MySQL Docker 容器..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 停止并删除现有容器（如果存在）
echo "🧹 清理现有容器..."
docker-compose down -v

# 启动 MySQL 容器
echo "📦 启动 MySQL 容器..."
docker-compose up -d mysql

# 等待 MySQL 启动
echo "⏳ 等待 MySQL 启动..."
sleep 30

# 检查 MySQL 是否启动成功
echo "🔍 检查 MySQL 连接..."
for i in {1..10}; do
    if docker exec collectloop_mysql mysql -uhisbridge -phisbridge123 -e "SELECT 1;" > /dev/null 2>&1; then
        echo "✅ MySQL 启动成功!"
        break
    else
        echo "⏳ 等待 MySQL 启动... ($i/10)"
        sleep 5
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ MySQL 启动失败，请检查日志:"
        docker logs collectloop_mysql
        exit 1
    fi
done

# 显示连接信息
echo ""
echo "📊 MySQL 连接信息:"
echo "  主机: localhost"
echo "  端口: 3306"
echo "  数据库: collectloop_db"
echo "  用户名: hisbridge"
echo "  密码: hisbridge123"
echo ""

# 可选：启动 phpMyAdmin
read -p "🌐 是否启动 phpMyAdmin 管理界面? (y/n): " start_phpmyadmin
if [[ $start_phpmyadmin =~ ^[Yy]$ ]]; then
    echo "🌐 启动 phpMyAdmin..."
    docker-compose up -d phpmyadmin
    echo "✅ phpMyAdmin 已启动，访问地址: http://localhost:8080"
    echo "   用户名: hisbridge"
    echo "   密码: hisbridge123"
fi

echo ""
echo "🎉 MySQL 环境准备完成!"
echo ""
echo "📝 接下来可以:"
echo "  1. 测试连接: go run test_mysql_connection.go"
echo "  2. 运行程序: go run main.go"
echo "  3. 查看日志: docker logs collectloop_mysql"
echo "  4. 停止服务: docker-compose down"
