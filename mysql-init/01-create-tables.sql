-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

USE collectloop_db;

-- 创建科室信息表
CREATE TABLE IF NOT EXISTS spt_department (
    department_id INT(11) AUTO_INCREMENT PRIMARY KEY COMMENT '科室ID',
    code VARCHAR(50) NOT NULL DEFAULT '' COMMENT '科室编码',
    name VARCHAR(50) NOT NULL DEFAULT '' COMMENT '科室名称',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室信息表';

-- 创建用户信息表
CREATE TABLE IF NOT EXISTS spt_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50) NOT NULL COMMENT '医护人员账号(可以是工号)',
    name VARCHAR(50) NOT NULL DEFAULT '' COMMENT '姓名',
    sex INT(2) DEFAULT 1 COMMENT '性别 1.男2.女',
    role_id INT(2) DEFAULT 1 COMMENT '1.医生2.护士',
    dept_id INT(11) DEFAULT 0 COMMENT '科室ID,与科室视图中department_id对应',
    inpatient_ward VARCHAR(500) DEFAULT '' COMMENT '所管理的病区,如果有多个使用半角逗号分隔',
    mobile VARCHAR(20) DEFAULT '' COMMENT '医生手机号',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
    UNIQUE KEY uk_user_name (user_name),
    INDEX idx_role_id (role_id),
    INDEX idx_dept_id (dept_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建患者信息表
CREATE TABLE IF NOT EXISTS spt_patient (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(32) NOT NULL DEFAULT '' COMMENT '患者姓名',
    id_card VARCHAR(32) DEFAULT '' COMMENT '身份证号',
    mobile VARCHAR(32) DEFAULT '' COMMENT '手机号码',
    sex VARCHAR(32) DEFAULT '' COMMENT '性别1.男2.女',
    age INT(2) DEFAULT 0 COMMENT '数字格式的年龄',
    birthday DATETIME NULL COMMENT '生日(格式:YYYY-MM-dd)',
    hospitalization_no VARCHAR(32) NOT NULL COMMENT '住院号,患者出入院多次,始终绑定该患者,不变',
    inhospital_diagnose VARCHAR(255) DEFAULT '' COMMENT '入院诊断',
    dept_id INT(11) DEFAULT 0 COMMENT '科室ID,与科室视图中department_id对应',
    sickbed_no VARCHAR(32) DEFAULT '' COMMENT '床位号',
    doctor_id VARCHAR(32) DEFAULT '' COMMENT '责任医生的用户ID,对应用户视图表中的user_name',
    nurse_id VARCHAR(32) DEFAULT '' COMMENT '责任护士的用户ID,对应用户视图表中的user_name',
    nurse_level INT(2) DEFAULT 1 COMMENT '护理级别1.一级2.二级3.三级4.特级护理',
    inhospital_time DATETIME NOT NULL COMMENT '入院时间',
    outhospital_time DATETIME NULL COMMENT '出院时间',
    status INT(2) DEFAULT 1 COMMENT '患者当前状态1.在院2.出院',
    catetegory VARCHAR(32) DEFAULT '' COMMENT '患者类别如,医保 自费其他等',
    inpatient_ward VARCHAR(32) DEFAULT '' COMMENT '病区 如男病区 女病区一病区等',
    inpatient_info_id VARCHAR(32) DEFAULT '' COMMENT '信息唯一ID,每次出入院一个ID编号',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
    INDEX idx_hospitalization_no (hospitalization_no),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_nurse_id (nurse_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_inhospital_time (inhospital_time),
    INDEX idx_outhospital_time (outhospital_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 创建科室视图
CREATE OR REPLACE VIEW view_spt_department AS
SELECT
    department_id,
    code,
    name,
    updated_at
FROM spt_department;

-- 创建用户视图
CREATE OR REPLACE VIEW view_spt_user AS
SELECT
    user_name,
    name,
    sex,
    role_id,
    dept_id,
    updated_at,
    inpatient_ward,
    mobile
FROM spt_user;

-- 创建患者视图
CREATE OR REPLACE VIEW view_spt_patient AS
SELECT
    name,
    id_card,
    mobile,
    sex,
    age,
    birthday,
    hospitalization_no,
    inhospital_diagnose,
    dept_id,
    sickbed_no,
    doctor_id,
    nurse_id,
    nurse_level,
    inhospital_time,
    outhospital_time,
    status,
    catetegory,
    inpatient_ward,
    inpatient_info_id,
    updated_at
FROM spt_patient;
