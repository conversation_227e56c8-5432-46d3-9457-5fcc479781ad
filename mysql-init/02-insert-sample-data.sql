-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 插入示例数据
USE collectloop_db;

-- 插入科室数据
INSERT INTO spt_department (department_id, code, name) VALUES
(101, 'NK001', '内科一科'),
(102, 'WK001', '外科一科'),
(103, 'GK001', '骨科'),
(104, 'FK001', '妇科'),
(105, 'EK001', '儿科'),
(201, 'HL001', '护理部一科'),
(202, 'HL002', '护理部二科');

-- 插入医生护士用户数据
INSERT INTO spt_user (user_name, name, sex, role_id, dept_id, inpatient_ward, mobile) VALUES
('DOC001', '张医生', 1, 1, 101, '内科一病区', '13800001001'),
('DOC002', '李医生', 2, 1, 102, '外科一病区', '13800001002'),
('DOC003', '王医生', 1, 1, 103, '骨科病区', '13800001003'),
('DOC004', '赵医生', 2, 1, 104, '妇科病区', '13800001004'),
('DOC005', '陈医生', 1, 1, 105, '儿科病区', '13800001005'),
('NURSE001', '护士小王', 2, 2, 201, '内科一病区,外科一病区', '13800002001'),
('NURSE002', '护士小李', 2, 2, 202, '骨科病区,妇科病区', '13800002002'),
('NURSE003', '护士小张', 1, 2, 201, '儿科病区', '13800002003');

-- 插入患者数据
INSERT INTO spt_patient (
    name, id_card, mobile, sex, age, birthday, hospitalization_no,
    inhospital_diagnose, dept_id, sickbed_no, doctor_id, nurse_id, nurse_level,
    inhospital_time, outhospital_time, status, catetegory, inpatient_ward, inpatient_info_id
) VALUES
-- 在院患者
('张三', '110101199001011234', '13800138001', '男', 35, '1990-01-01', 'ZY202501001',
 '高血压病', 101, 'A101', 'DOC001', 'NURSE001', 2,
 '2025-01-01 10:30:00', NULL, 1, '医保', '内科一病区', 'ZY2025010110:30:00'),

('李四', '110101199002022345', '13800138002', '女', 28, '1990-02-02', 'ZY202501002',
 '阑尾炎', 102, 'A102', 'DOC002', 'NURSE002', 1,
 '2025-01-02 14:20:00', NULL, 1, '自费', '外科一病区', 'ZY2025010214:20:00'),

('王五', '110101199003033456', '13800138003', '男', 42, '1990-03-03', 'ZY202501003',
 '骨折', 103, 'B201', 'DOC003', 'NURSE001', 3,
 '2025-01-03 09:15:00', NULL, 1, '医保', '骨科病区', 'ZY2025010309:15:00'),

('赵六', '110101199004044567', '13800138004', '女', 55, '1990-04-04', 'ZY202501004',
 '子宫肌瘤', 104, 'C301', 'DOC004', 'NURSE002', 2,
 '2025-01-04 16:45:00', NULL, 1, '医保', '妇科病区', 'ZY2025010416:45:00'),

('小明', '110101201501015678', '13800138010', '男', 10, '2015-01-01', 'ZY202501007',
 '肺炎', 105, 'D101', 'DOC005', 'NURSE003', 1,
 '2025-01-05 08:30:00', NULL, 1, '医保', '儿科病区', 'ZY2025010508:30:00'),

-- 已出院患者
('孙七', '110101199005055678', '13800138005', '男', 38, '1990-05-05', 'ZY202412001',
 '胃炎', 101, 'A103', 'DOC001', 'NURSE001', 2,
 '2024-12-01 08:30:00', '2024-12-15 10:00:00', 2, '医保', '内科一病区', 'ZY2024120108:30:00'),

('周八', '110101199006066789', '13800138006', '女', 45, '1990-06-06', 'ZY202412002',
 '胆囊炎', 102, 'B202', 'DOC002', 'NURSE002', 1,
 '2024-12-02 11:20:00', '2024-12-20 14:30:00', 2, '自费', '外科一病区', 'ZY2024120211:20:00'),

('吴九', '110101199007077890', '13800138007', '男', 32, '1990-07-07', 'ZY202412003',
 '韧带损伤', 103, 'C302', 'DOC003', 'NURSE001', 3,
 '2024-12-03 15:10:00', '2024-12-25 09:45:00', 2, '医保', '骨科病区', 'ZY2024120315:10:00'),

-- 最近入院的患者（用于测试时间戳功能）
('郑十', '110101199008088901', '13800138008', '女', 29, '1990-08-08', 'ZY202501008',
 '糖尿病', 101, 'A104', 'DOC005', 'NURSE001', 2,
 NOW() - INTERVAL 2 HOUR, NULL, 1, '医保', '内科一病区', CONCAT('ZY', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'))),

('钱十一', '110101199009099012', '13800138009', '男', 51, '1990-09-09', 'ZY202501009',
 '疝气', 102, 'B203', 'DOC001', 'NURSE002', 1,
 NOW() - INTERVAL 1 HOUR, NULL, 1, '自费', '外科一病区', CONCAT('ZY', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')));

-- 验证数据插入
SELECT '=== 数据统计报告 ===' as info;

SELECT '科室数据统计:' as info;
SELECT department_id, code, name FROM view_spt_department ORDER BY department_id;

SELECT '用户数据统计:' as info;
SELECT
    role_id,
    CASE role_id
        WHEN 1 THEN '医生'
        WHEN 2 THEN '护士'
        ELSE '其他'
    END as role_name,
    COUNT(*) as count
FROM view_spt_user
GROUP BY role_id;

SELECT '患者数据统计:' as info;
SELECT
    status,
    CASE status
        WHEN 1 THEN '在院'
        WHEN 2 THEN '出院'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM view_spt_patient
GROUP BY status;

SELECT '病区分布统计:' as info;
SELECT inpatient_ward, COUNT(*) as patient_count
FROM view_spt_patient
WHERE status = 1
GROUP BY inpatient_ward
ORDER BY patient_count DESC;

SELECT '医生负责患者统计:' as info;
SELECT
    u.name as doctor_name,
    u.user_name as doctor_id,
    COUNT(p.name) as patient_count
FROM view_spt_user u
LEFT JOIN view_spt_patient p ON u.user_name = p.doctor_id AND p.status = 1
WHERE u.role_id = 1
GROUP BY u.user_name, u.name
ORDER BY patient_count DESC;
