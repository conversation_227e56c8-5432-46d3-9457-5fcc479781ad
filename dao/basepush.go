package dao

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"gitlab.itingluo.com/backend/ivankabasepush/models"
	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit"
)

type AF_Doctor_1 struct {
	UserName string `gorm:"type:varchar(50);column:user_name"` // 医生编号
	DeptId   int64  `gorm:"type:bigint(50);column:dept_id"`    // 医生编号
	Name     string `gorm:"type:varchar(50);column:name"`      // 医生名字
	RoleId   int64  `gorm:"type:bigint(50);column:role_id"`    // 医生全名
}

// 获取医生信息直接到数据库 12小时同步一次
func GetAfDoctorList(tl_client_id string, Dbname string, TlClientName string) error {
	var host string
	if os.Getenv("CONFIGOR_ENV") == "ivktest" {
		host = "https://api-sit.itingluo.com"
	} else {
		host = "https://api.itingluo.com"
	}
	log.Print(host)
	var doctorList []*AfDoctorItem
	var results []*AF_Doctor_1

	switch Dbname {
	case "sqlserver":
		log.Println("连接到 SQL Server 数据库")
		db := models.DB()
		if tl_client_id == "1000000000385" {
			err := db.Table("His.view_spt_user").Select(" role_id, dept_id, user_name, name").Where("user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id )").Find(&results).Error
			if err != nil {
				log.Print("SQL Server 查询错误: %v", err)
				return err
			}

		} else {
			err := db.Table("view_spt_user").Select("user_name, name").Where("user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id )").Find(&results).Error
			if err != nil {
				log.Print("SQL Server 查询错误: %v", err)
				return err
			}
		}

	case "sybase":
		log.Println("连接到 Sybase 数据库")
		db := models.SybaseDB()
		rows, err := db.Query("select coalesce(role_id,0), coalesce(dept_id,0), cast(user_name as VARBINARY) as user_name, cast(name as VARBINARY) as name from view_spt_user")
		if err != nil {
			log.Print("查询错误: %v", err)
			return err
		}
		defer rows.Close()
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			var deptID, roleID int64
			err := rows.Scan(&roleID, &deptID, &userName, &name)
			if err != nil {
				log.Print("查询错误: %v", err)
			}
			afDoctor.Name = cy(strings.TrimSpace(name))
			afDoctor.UserName = cy(strings.TrimSpace(userName))
			afDoctor.DeptId = deptID
			afDoctor.RoleId = roleID
			results = append(results, &afDoctor)
		}
	case "mysql":
		log.Println("连接到 MySQL 数据库")
		db := models.MysqlDB()
		rows, err := db.Query("SELECT COALESCE(user_name,''), COALESCE(name,'') FROM view_spt_user WHERE user_name IN (SELECT doctor_id FROM view_spt_patient GROUP BY doctor_id)")
		if err != nil {
			log.Printf("MySQL 查询错误: %v", err)
			return err
		}
		defer rows.Close()
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			err := rows.Scan(&userName, &name)
			if err != nil {
				log.Printf("MySQL 扫描错误: %v", err)
			}
			afDoctor.Name = strings.TrimSpace(name)
			afDoctor.UserName = strings.TrimSpace(userName)
			results = append(results, &afDoctor)
		}
	case "oracle":
		log.Println("连接到 Oracle 数据库")
		db := models.OraclDB()
		rows, err := db.Query("select  coalesce(user_name,''), coalesce(name,'') from view_spt_user t where user_name in (SELECT doctor_id from view_spt_patient GROUP BY doctor_id)")
		if err != nil {
			log.Printf("查询错误: %v", err)
			return err
		}
		defer rows.Close()
		for rows.Next() {
			var afDoctor AF_Doctor_1
			var userName, name string
			err := rows.Scan(&userName, &name)
			if err != nil {
				log.Printf("扫描错误: %v", err)
			}
			afDoctor.Name = strings.TrimSpace(name)
			afDoctor.UserName = strings.TrimSpace(userName)
			results = append(results, &afDoctor)
		}
	}

	for _, v := range results {
		doctorList = append(doctorList, &AfDoctorItem{
			DoctorName: v.Name,
			DoctorNo:   v.UserName,
		})
		//log.Println("医生姓名:", v.Name)
		//log.Println("医生编号:", v.UserName)
	}

	log.Println("医生列表长度:", len(doctorList))
	url := fmt.Sprintf("%s/apiv1/psyhis/doctorlsit/af_save?tl_client_id=%s&tl_client_name=%s", host, tl_client_id, TlClientName)

	if len(doctorList) > 0 {
		respInfo, err := toolkit.DoRequestPost(&SendAfDoctorListReq{
			Count:  int64(len(doctorList)),
			Doctor: doctorList,
		}, url)
		if err != nil {
			return fmt.Errorf("POST请求错误: %v", err)
		}
		log.Println("GetAfDoctorList 响应:", string(respInfo), url)
	}
	return nil
}

type SendAfDoctorListReq struct {
	Count  int64           `json:"count"`
	Doctor []*AfDoctorItem `json:"doctor_list"`
}

type AfDoctorItem struct {
	DoctorName string `json:"doctor_name"`
	DoctorNo   string `json:"doctor_no"`
}

func TickInPatientByRedis(d time.Duration, Status string, DbName string) {
	defer func() {
		if err := recover(); err != nil {
			std.LogRecover(err)
		}
	}()
	tickChan := time.NewTicker(d).C
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId)
	for {
		select {
		case <-tickChan:
			InPatientToRedis(tlClientId, Status, DbName)
		}
	}
}
func TickDoctorByRedis(d time.Duration, Status string, DbName string, TlClientName string) {
	defer func() {
		if err := recover(); err != nil {
			std.LogRecover(err)
		}
	}()
	tickChan := time.NewTicker(d).C
	tlClientId := strconv.Itoa(models.Hospital.C.TlClientId)
	for {
		select {
		case <-tickChan:
			GetAfDoctorList(tlClientId, DbName, TlClientName)
		}
	}
}
