package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/sirupsen/logrus"
	"gitlab.itingluo.com/backend/ivankabasepush/cache"
	"gitlab.itingluo.com/backend/ivankabasepush/models"
	std "gitlab.itingluo.com/backend/ivankastd"
	"golang.org/x/text/encoding/simplifiedchinese"
)

type PostPatientLastTimeReq struct {
	TlClientId int `json:"tl_client_id"`
}

type Patients struct {
	Name              string `json:"name"`
	IDCard            string `json:"id_card"`
	Mobile            string `json:"mobile"`
	Sex               string `json:"sex"`
	Age               int    `json:"age"`
	HospitalizationNo string `json:"hospitalization_no"`
	SickbedNo         string `json:"sickbed_no"`
	InhospitalTime    int64  `json:"inhospital_time"`
	OuthospitalTime   int64  `json:"outhospital_time"`
	Status            int    `json:"status"`
	Category          string `json:"category" gorm:"column:catetegory"`
	InpatientWard     string `json:"inpatient_ward"`
	DoctorID          string `json:"doctor_id"`
	InpatientInfoId   string `json:"inpatient_info_id"`
	NurseId           string `json:"nurse_id"`
	Birthday          string `json:"birthday"`
}

// InPatientToRedis 从视图中获取在院病人写入redis
func InPatientToRedis(tlClientId string, Status string, DbName string) {
	log.Println(Status + "begin" + DbName)
	resIn := make([]Patients, 0)
	err := GetPatient(&resIn, Status, tlClientId, DbName)
	if err != nil {
		if err.Error() == "redis: nil" {
			return
		}
		std.LogErrorLnAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tlClientId,
		}, "first", "writeRedis", err)
	}
	SavePatientByRedis(&resIn, Status, tlClientId)
}

func GetPatient(res *[]Patients, status string, tlClientId string, DbName string) error {
	tid, err := strconv.Atoi(tlClientId)
	log.Println("tl_client_id:", tid)
	if err != nil {
		return err
	}
	var lastTime int
	if status != "Up" {
		lastTime, err = GetLastTimeByRedis(tid, status)
		if err != nil {
			log.Println(err)
			return err
		}
		if lastTime == 0 {
			log.Println("lastTime==0")
			return nil
		}
	}

	var totalRecords int64
	var w string
	var query string
	switch status {
	case "In":
		if DbName == "oracle" {
			w = fmt.Sprintf("inhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime+1)
		} else if DbName == "mysql" {
			w = fmt.Sprintf("inhospital_time > DATE_ADD('1970-01-01 08:00:00', INTERVAL %d SECOND)", lastTime+1)
		} else {
			w = fmt.Sprintf("inhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00')", lastTime+1)
		}
	case "Out":
		//处理redis返回的键值返回的数据是否是当天整点的时间戳,如果是则获取五个月前的数据
		//lastTime := int64(lastTime)
		//lastTimeAsTime := time.Unix(lastTime, 0)
		//today := time.Date(lastTimeAsTime.Year(), lastTimeAsTime.Month(), lastTimeAsTime.Day(), 0, 0, 0, 0, lastTimeAsTime.Location())
		//todayTimestamp := today.Unix()
		//log.Println("出院时间戳:", todayTimestamp)
		//log.Println("lastTime", lastTime)
		if DbName == "oracle" {
			//if todayTimestamp == lastTime {
			//	now := time.Now()
			//	// 减去5个月
			//	fiveMonthsAgo := now.AddDate(0, -5, 0)
			//	log.Println(fiveMonthsAgo)
			//	// 获取Unix时间戳
			//	timestampFive := fiveMonthsAgo.Unix()
			//	fmt.Println(timestampFive)
			//	w = fmt.Sprintf("outhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime)
			//} else {
			w = fmt.Sprintf("outhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime)
			//}
		} else if DbName == "mysql" {
			w = fmt.Sprintf("outhospital_time > DATE_ADD('1970-01-01 08:00:00', INTERVAL %d SECOND) and status=2", lastTime)
		} else {

			//if todayTimestamp == lastTime {
			//	now := time.Now()
			//	// 减去5个月
			//	fiveMonthsAgo := now.AddDate(0, -5, 0)
			//	log.Println(fiveMonthsAgo)
			//	// 获取Unix时间戳
			//	timestampFive := fiveMonthsAgo.Unix()
			w = fmt.Sprintf("outhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00') and status=2", lastTime)
			//}
			//else {
			//	w = fmt.Sprintf("outhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00')", todayTimestamp+1)
			//}
		}

	case "Up":
		switch tid {
		case 1000000000332:
			w = " outhospital_time='1900-01-01 00:00:00.000'"
		case 1000000000292:
			w = " outhospital_time is null"
		case 1000000000302:
			w = " outhospital_time is null or status = 1"
		case 1000000000356:
			w = " outhospital_time is null or status = 1"
		default:
			w = "status = 1"
		}

	}
	switch DbName {
	//处理sqlserver
	case "sqlserver":
		db := models.DB()
		switch tid {
		case 1000000000385:
			if err := db.Table("His.view_spt_patient as vsp").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"CASE WHEN outhospital_time = '1900-01-01 00:00:00.000' THEN 0 ELSE DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) END AS outhospital_time," +
				"status,catetegory,(SELECT name FROM His.view_spt_department WHERE department_id = vsp.dept_id) AS inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		case 1000000000365:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"(CASE WHEN outhospital_time = '1970-01-01 00:00:00.000' THEN 0 else CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) end )AS outhospital_time," +
				"status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		case 1000000000332:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"(CASE WHEN outhospital_time < '1970-01-01' THEN 0 else CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) end )AS outhospital_time," +
				"CASE WHEN outhospital_time = '1900-01-01 00:00:00.000' THEN 1 else 2 END as status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		default:
			if err := db.Table("view_spt_patient").Select("name,id_card,mobile,sex,age," +
				"hospitalization_no,sickbed_no," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', inhospital_time) AS bigint) AS inhospital_time," +
				"CAST(DATEDIFF(SECOND, '1970-01-01 08:00:00', outhospital_time) AS bigint) AS outhospital_time," +
				"status,catetegory,inpatient_ward,doctor_id,inpatient_info_id,nurse_id,birthday").Where(w).Count(&totalRecords).Find(&res).Error; err != nil {
				return err
			}
		}

		for i, _ := range *res {
			(*res)[i].Name = strings.TrimSpace((*res)[i].Name)
			(*res)[i].IDCard = strings.TrimSpace((*res)[i].IDCard)
			(*res)[i].Mobile = strings.TrimSpace((*res)[i].Mobile)
			(*res)[i].Sex = strings.TrimSpace((*res)[i].Sex)
			if tid == 1000000000365 {
				(*res)[i].HospitalizationNo = strings.TrimSpace((*res)[i].InpatientInfoId)
			} else {
				(*res)[i].HospitalizationNo = strings.TrimSpace((*res)[i].HospitalizationNo)
			}
			(*res)[i].SickbedNo = strings.TrimSpace((*res)[i].SickbedNo)
			(*res)[i].Category = strings.TrimSpace((*res)[i].Category)
			(*res)[i].InpatientWard = strings.TrimSpace((*res)[i].InpatientWard)
			switch tid {
			//大余瑞康医院
			case 1000000000370:
				(*res)[i].DoctorID = "3"
			//南昌康宁医院
			case 1000000000359:
				(*res)[i].DoctorID = "10176"
			default:
				(*res)[i].DoctorID = strings.TrimSpace((*res)[i].DoctorID)
			}
			(*res)[i].Status = (*res)[i].Status

			(*res)[i].InpatientInfoId = strings.TrimSpace((*res)[i].InpatientInfoId)
			(*res)[i].NurseId = strings.TrimSpace((*res)[i].NurseId)
			(*res)[i].Birthday = strings.TrimSpace((*res)[i].Birthday)
		}
		return nil
		//处理oracle
	case "oracle":
		db := models.OraclDB()
		switch tid {
		//	万年惠康 //
		case 1000000000292:
			query = "SELECT " +
				"name, " +
				"COALESCE(id_card, '') AS id_card, " +
				"COALESCE(mobile, '') AS mobile, " +
				"sex, " +
				"age, " +
				"hospitalization_no, " +
				"sickbed_no, " +
				"CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0)) AS inhospital_time, " +
				"CASE " +
				"WHEN outhospital_time is null THEN 0 " +
				"ELSE CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0)) " +
				"END AS outhospital_time, " +
				"case when outhospital_time is null then 1 else 2 end as status, " +
				"catetegory, " +
				"inpatient_ward, " +
				"doctor_id, " +
				"CONCAT(hospitalization_no, TO_CHAR(inhospital_time, 'YYYY-MM-DD')) as inpatient_info_id ,nurse_id ,birthday " +
				"FROM " +
				"view_spt_patient " +
				"WHERE " + w
			//高安
		case 1000000000252:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile,sex, EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM birthday) AS age, hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"coalesce((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))),0) AS outhospital_time, " +
				"status,category,inpatient_ward, doctor_id, inpatient_info_id ,nurse_id ,birthday " +
				"FROM VIEW_ZY_DETAIL_PATIENT " +
				"WHERE " + w
		case 1000000000356:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile,sex, case when age=0 then EXTRACT(YEAR FROM SYSDATE) - EXTRACT(YEAR FROM birthday) ELSE age END AS age, hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"coalesce((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))),0) AS outhospital_time, " +
				"case when status is null then case when outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status,catetegory,case when inpatient_ward = 106 then '女病区' when inpatient_ward=181 then '男病区' else inpatient_ward end as inpatient_ward, doctor_id, inpatient_info_id ,nurse_id ,birthday " +
				"FROM view_spt_patient " +
				"WHERE " + w
		default:
			query = "SELECT " +
				"name, coalesce(id_card,''), mobile, sex, " +
				"COALESCE(age, 0) AS age, " +
				"hospitalization_no, sickbed_no, " +
				"(CAST((inhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))) AS inhospital_time, " +
				"COALESCE((CAST((outhospital_time - TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 AS NUMBER(18,0))), 0) AS outhospital_time, " +
				"CASE WHEN status IS NULL THEN CASE WHEN outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status, " +
				"catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday " +
				"FROM view_spt_patient " +
				"WHERE " + w
		}
		log.Printf(query)
		rows, err := db.Query(query)
		if err != nil {
			log.Println(err)
			return err
		}
		defer rows.Close()
		var totalRecords int64
		for rows.Next() {
			var patient Patients
			var (
				name               string
				id_card            string
				mobile             string
				sex                string
				age                int
				hospitalization_no string
				sickbed_no         string
				doctor_id          string
				inhospital_time    int64
				outhospital_time   int64
				status1            int
				catetegory         string // 分类
				inpatient_ward     string // 病区
				inpatient_info_id  string // 每次出入院一个ID
				nurse_id           string
				birthday           string
			)
			err = rows.Scan(&name, &id_card, &mobile, &sex, &age, &hospitalization_no, &sickbed_no, &inhospital_time, &outhospital_time, &status1, &catetegory, &inpatient_ward, &doctor_id, &inpatient_info_id, &nurse_id, &birthday)
			if err != nil {
				//log.Print(name)
				log.Printf("query error1", err)
				return err
			}
			patient.Name = strings.TrimSpace(name)
			patient.IDCard = strings.TrimSpace(id_card)
			patient.Mobile = strings.TrimSpace(mobile)
			patient.Sex = strings.TrimSpace(sex)
			patient.Age = age
			patient.HospitalizationNo = strings.TrimSpace(hospitalization_no)
			patient.SickbedNo = strings.TrimSpace(sickbed_no)
			patient.Category = strings.TrimSpace(catetegory)
			patient.InpatientWard = strings.TrimSpace(inpatient_ward)
			patient.DoctorID = strings.TrimSpace(doctor_id)
			patient.InhospitalTime = inhospital_time
			patient.OuthospitalTime = outhospital_time
			patient.InpatientInfoId = strings.TrimSpace(inpatient_info_id)
			patient.Status = status1
			patient.NurseId = nurse_id
			patient.Birthday = birthday
			*res = append(*res, patient)
			totalRecords++
		}
		log.Println(totalRecords)
		if err := rows.Err(); err != nil {
			log.Println(err)
			return err
		}
		return nil

	//处理mysql
	case "mysql":
		db := models.MysqlDB()
		query = "SELECT " +
			"name, " +
			"COALESCE(id_card, '') AS id_card, " +
			"COALESCE(mobile, '') AS mobile, " +
			"sex, " +
			"COALESCE(age, 0) AS age, " +
			"hospitalization_no, " +
			"sickbed_no, " +
			"UNIX_TIMESTAMP(inhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') AS inhospital_time, " +
			"CASE " +
			"WHEN outhospital_time IS NULL THEN 0 " +
			"ELSE UNIX_TIMESTAMP(outhospital_time) - UNIX_TIMESTAMP('1970-01-01 08:00:00') " +
			"END AS outhospital_time, " +
			"CASE WHEN status IS NULL THEN CASE WHEN outhospital_time IS NULL THEN 1 ELSE 2 END ELSE status END AS status, " +
			"catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday " +
			"FROM view_spt_patient " +
			"WHERE " + w
		log.Printf(query)
		rows, err := db.Query(query)
		if err != nil {
			log.Println(err)
			return err
		}
		defer rows.Close()
		for rows.Next() {
			var patient Patients
			var name, id_card, mobile, sex, hospitalization_no, sickbed_no, catetegory, inpatient_ward, doctor_id, inpatient_info_id, nurse_id, birthday string
			var age, inhospital_time, outhospital_time, status1 int64
			err = rows.Scan(&name, &id_card, &mobile, &sex, &age, &hospitalization_no, &sickbed_no, &inhospital_time, &outhospital_time, &status1, &catetegory, &inpatient_ward, &doctor_id, &inpatient_info_id, &nurse_id, &birthday)
			if err != nil {
				log.Printf("MySQL query error: %v", err)
				return err
			}
			patient.Name = strings.TrimSpace(name)
			patient.IDCard = strings.TrimSpace(id_card)
			patient.Mobile = strings.TrimSpace(mobile)
			patient.Sex = strings.TrimSpace(sex)
			patient.Age = int(age)
			patient.HospitalizationNo = strings.TrimSpace(hospitalization_no)
			patient.SickbedNo = strings.TrimSpace(sickbed_no)
			patient.Category = strings.TrimSpace(catetegory)
			patient.InpatientWard = strings.TrimSpace(inpatient_ward)
			patient.DoctorID = strings.TrimSpace(doctor_id)
			patient.InhospitalTime = inhospital_time
			patient.OuthospitalTime = outhospital_time
			patient.InpatientInfoId = strings.TrimSpace(inpatient_info_id)
			patient.Status = int(status1)
			patient.NurseId = nurse_id
			patient.Birthday = birthday
			*res = append(*res, patient)
			totalRecords++
		}
		log.Println(totalRecords)
		if err := rows.Err(); err != nil {
			log.Println(err)
			return err
		}
		return nil

	}
	log.Println("未发现数据库")
	return nil
}

func GetLastTimeByRedis(tlClientId int, status string) (int, error) {
	rs := cache.R()
	var lastTime int
	switch status {
	case "In":
		in, err := rs.Get(context.Background(), "InLastTime-"+strconv.Itoa(tlClientId)).Result()
		if err != nil {
			return lastTime, err
		}
		lastTime, err = strconv.Atoi(in)
		if err != nil {
			return lastTime, err
		}
		rs.Del(context.Background(), "InLastTime-"+strconv.Itoa(tlClientId))
		return lastTime, nil
	case "Out":
		out, err := rs.Get(context.Background(), "OutLastTime-"+strconv.Itoa(tlClientId)).Result()
		if err != nil {
			return lastTime, err
		}
		lastTime, err = strconv.Atoi(out)
		if err != nil {
			return lastTime, err
		}
		rs.Del(context.Background(), "OutLastTime-"+strconv.Itoa(tlClientId))
		return lastTime, nil
	case "Up":
		return 0, nil
	default:
		return lastTime, fmt.Errorf("status ERR")
	}
}

func SavePatientByRedis(data *[]Patients, status string, tid string) {
	rs := cache.R()
	RedisMap1 := make(map[string]string)
	RedisMap2 := make(map[string]string)
	for _, patients := range *data {
		key := patients.HospitalizationNo + strconv.FormatInt(patients.InhospitalTime, 10)
		binaryData, err := structToJSON(patients)
		if err != nil {
			fmt.Println("structToBinary:", err)
		}
		if patients.Status == 1 { //1在院，2出院
			RedisMap1[key] = binaryData
		} else {
			RedisMap2[key] = binaryData
		}
	}
	var err error
	switch status {
	case "In":
		if len(RedisMap1) == 0 {
			return
		}
		err = rs.HMSet(context.Background(), "InPatient-"+tid, RedisMap1).Err()
		std.LogInfoAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
			"status":     status,
		}, len(RedisMap1))
	case "Out":
		if len(RedisMap2) == 0 {
			return
		}
		err = rs.HMSet(context.Background(), "OutPatient-"+tid, RedisMap2).Err()
		std.LogInfoAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
			"status":     status,
		}, len(RedisMap2))
	case "Up":
		if len(RedisMap1) == 0 {
			return
		}
		err = rs.Del(context.Background(), "UpData-"+tid).Err()
		err = rs.HMSet(context.Background(), "UpData-"+tid, RedisMap1).Err()
	}
	if err != nil {
		std.LogErrorLnAndFields(logrus.Fields{
			"topic":      "writeRedis",
			"tlClientId": tid,
		}, status, "SavePatientByRedis:", err)
	}
}

// 结构体转换为JSON字符串
func structToJSON(data interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// 处理转码cp850->utf8
func cy(string2 string) string {
	decoder := simplifiedchinese.GB18030.NewDecoder()
	NameUtf8Bytes, err := decoder.Bytes([]byte(string2))
	if err != nil {
		fmt.Println("解码出错:", err)
		return "err"
	}
	return string(NameUtf8Bytes)
}
