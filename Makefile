.PHONY: list sync-codes build deps test

list:
	@echo "sync-codes: 同步代码"
	@echo "build: 编译可执行文件"
	@echo "test: 测试"

sync-codes:
	git pull

build:
	PATH=/tmp/govendor/bin:$(PATH)
	GOPATH=/tmp/govendor/:$(GOPATH)

	# 运行 go mod tidy 来整理依赖项
	go mod tidy

	# 编译项目
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -ldflags '-w' -o server ./main.go

deps:
	mkdir -p /tmp/govendor_src/src/gitlab.wallstcn.com/wscnbackend
	@if [ "$(CI_COMMIT_REF_NAME)" = "master" ]; then\
		echo "checkout ivankastd:master";\
		git clone -b release ssh://**********************:8082/backend/ivankastd.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd;\
		git clone -b main ssh://***********************:8082/backend/ivankasecret.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret;\
		git clone ssh://**********************:8082/backend/maskprotocol.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol;\
		git clone ssh://**********************:8082/backend/micro.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/micro;\
	else\
		echo "checkout ivankastd:sit";\
		git clone -b release ssh://**********************:8082/backend/ivankastd.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd;\
		git clone -b main ssh://***********************:8082/backend/ivankasecret.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret;\
		git clone ssh://**********************:8082/backend/maskprotocol.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol;\
		git clone ssh://**********************:8082/backend/micro.git /tmp/govendor_src/src/gitlab.itingluo.com/backend/micro;\
	fi

	rm -rf "/builds/backend/ivankastd/"
	rm -rf "/builds/backend/ivankasecret/"
	rm -rf "/builds/backend/maskprotocol/"
	rm -rf "/builds/backend/micro/"

	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankastd" "/builds/backend/ivankastd/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankaprotocol" "/builds/backend/maskprotocol/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/ivankasecret" "/builds/backend/ivankasecret/"
	cp -R "/tmp/govendor_src/src/gitlab.itingluo.com/backend/micro" "/builds/backend/micro/"

test:
	go test
