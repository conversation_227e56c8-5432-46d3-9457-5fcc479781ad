# 分析当前项目的业务逻辑
_Exported on 2025/7/7 at GMT+8 11:22:08 from Cursor (1.1.6)_

---

**User**

帮我分析下当前项目的整体业务逻辑 接收同步了哪些数据

---

**Cursor**

我将帮您分析这个项目的业务逻辑和同步的数据。先查看项目的核心文件。

让我查看一下模型定义，了解项目处理的数据结构：

让我看一下数据访问对象的实现:

让我看一下系统如何处理患者数据:

让我看下系统使用的缓存机制：

最后让我查看配置文件以了解整个系统的配置：

根据代码分析，我现在为您总结这个项目的业务逻辑和同步的数据：

## 项目整体业务逻辑

这是一个名为"collectloop"的医院数据同步系统，主要功能是从医院信息系统(HIS)中提取患者和医生数据，然后将这些数据同步到Redis缓存和远程API。项目支持连接多种数据库类型，包括SQL Server、Oracle和Sybase。

### 核心功能：

1. **数据同步**：定期从医院数据库获取患者和医生信息，并同步到Redis和远程API
2. **多数据库支持**：支持从SQL Server、Oracle和Sybase数据库中获取数据
3. **定时任务**：通过定时器定期执行同步任务
4. **数据缓存**：使用Redis缓存数据，提高系统性能

### 同步的数据：

1. **患者信息**：
   - 住院患者数据（`In`状态）
   - 出院患者数据（`Out`状态）
   - 在院患者数据（`Up`状态）
   - 患者信息包括：姓名、身份证号、手机号、性别、年龄、住院号、床位号、入院时间、出院时间、状态、类别、病房、医生ID等

2. **医生信息**：
   - 医生姓名
   - 医生编号
   - 科室ID
   - 角色ID

### 同步机制：

1. **入院患者同步**：每1分钟执行一次，同步最新入院患者数据
2. **出院患者同步**：每5分钟执行一次，同步最新出院患者数据 
3. **在院患者同步**：每2分钟执行一次，同步所有在院患者数据
4. **医生信息同步**：每12小时执行一次，同步医生数据

系统主要通过Redis管理时间戳，追踪最后同步的时间点，确保只同步新的数据。数据通过API发送到远程服务器，同时保持本地Redis缓存的更新。

系统通过配置文件设置数据库连接信息，支持不同医院的定制化需求，包括特殊查询条件和数据处理逻辑。

---

**User**

住院患者数据（In状态）
出院患者数据（Out状态）
在院患者数据（Up状态）
患者信息包括：姓名、身份证号、手机号、性别、年龄、住院号、床位号、入院时间、出院时间、状态、类别、病房、医生ID等的整体业务逻辑帮我分析啊下

---

**Cursor**

# 患者数据处理业务逻辑分析

项目中对三种患者状态数据的处理逻辑如下：

## 1. 数据同步频率设计

- **入院患者(In状态)**: 每1分钟同步一次
- **出院患者(Out状态)**: 每5分钟同步一次
- **在院患者(Up状态)**: 每2分钟同步一次

这种设计反映了不同数据的业务重要性和更新频率需求：入院信息需要最及时获取，在院信息次之，出院信息变动频率最低。

## 2. 查询条件逻辑

### 入院患者(In状态)
```go
// SQL Server
w = fmt.Sprintf("inhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00')", lastTime+1)
// Oracle
w = fmt.Sprintf("inhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime+1)
```
- 增量同步：只获取上次同步时间之后入院的新患者
- 系统通过Redis存储最后同步时间戳，确保不重复同步

### 出院患者(Out状态)
```go
// SQL Server
w = fmt.Sprintf("outhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00') and status=2", lastTime)
// Oracle
w = fmt.Sprintf("outhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime)
```
- 增量同步：获取上次同步后新出院的患者
- status=2表示已出院状态

### 在院患者(Up状态)
```go
// 不同医院有不同的逻辑
case 1000000000332:
    w = " outhospital_time='1900-01-01 00:00:00.000'"
case 1000000000292:
    w = " outhospital_time is null"
case 1000000000302:
    w = " outhospital_time is null or status = 1"
default:
    w = "status = 1"
```
- 全量同步：每次获取所有在院患者
- 根据不同医院定制查询条件，适应各医院数据结构差异

## 3. 数据处理流程

1. **获取上次同步时间**
   ```go
   lastTime, err = GetLastTimeByRedis(tid, status)
   ```
   
2. **构建查询条件**
   - 根据患者状态和数据库类型构建不同SQL条件

3. **执行数据库查询**
   - 根据医院ID(tl_client_id)选择特定查询语句
   - 对不同数据库类型使用对应语法

4. **数据清洗与标准化**
   ```go
   (*res)[i].Name = strings.TrimSpace((*res)[i].Name)
   ```
   - 去除字段空格
   - 特定医院的数据映射(如特定医院固定医生ID)

5. **数据存储与传输**
   ```go
   SavePatientByRedis(&resIn, Status, tlClientId)
   ```
   - 将处理后的数据存入Redis
   - 通过API发送到远程服务器

## 4. 特殊处理逻辑

1. **字段映射与转换**
   - 时间戳转换：数据库日期转UNIX时间戳
   - 特殊空值处理：如出院时间为null时转为0
   - 字段重映射：如万年惠康医院拼接住院号和入院日期作为住院信息ID

2. **医院特殊逻辑**
   ```go
   //大余瑞康医院
   case 1000000000370:
       (*res)[i].DoctorID = "3"
   //南昌康宁医院
   case 1000000000359:
       (*res)[i].DoctorID = "10176"
   ```
   - 某些医院使用固定医生ID
   - 不同医院有不同的出院患者判定标准

3. **多数据库兼容**
   - 为SQL Server、Oracle和Sybase提供不同SQL语法
   - 适配不同数据库的时间处理函数和数据类型

这种设计使系统能灵活适应不同医院的HIS系统，在保证数据一致性的同时，满足各医院的特殊需求。

```go
// SQL Server
w = fmt.Sprintf("inhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00')", lastTime+1)
// Oracle
w = fmt.Sprintf("inhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime+1)
```

```go
// SQL Server
w = fmt.Sprintf("outhospital_time > DATEADD(SECOND, %d, '1970-01-01 08:00:00') and status=2", lastTime)
// Oracle
w = fmt.Sprintf("outhospital_time > TO_DATE('1970-01-01 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + NUMTODSINTERVAL(%d, 'SECOND')", lastTime)
```

```go
// 不同医院有不同的逻辑
case 1000000000332:
    w = " outhospital_time='1900-01-01 00:00:00.000'"
case 1000000000292:
    w = " outhospital_time is null"
case 1000000000302:
    w = " outhospital_time is null or status = 1"
default:
    w = "status = 1"
```

```go
   lastTime, err = GetLastTimeByRedis(tid, status)
```

```go
   (*res)[i].Name = strings.TrimSpace((*res)[i].Name)
```

```go
   SavePatientByRedis(&resIn, Status, tlClientId)
```

```go
   //大余瑞康医院
   case 1000000000370:
       (*res)[i].DoctorID = "3"
   //南昌康宁医院
   case 1000000000359:
       (*res)[i].DoctorID = "10176"
```

