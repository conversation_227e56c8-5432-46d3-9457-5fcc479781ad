micro:
  prometheus:
    enabled: true
    batch_interval: **********
    collectors:
      - addr: "**********:9091"
    namespace: pushproc

log:
  level: 4
  output_dest: "console"
  #path: "/opt/log/collectloop"

logger:
  level: 5
  output_dest: "file"
  path: "/opt/log/ivankadedicated-api"

oracle:
  username: "INTERFACE_LB"
  password: "INTERFACE_LB"
  host: "**************:1521/ythis"   #**************  tingluo:new ************ ************  ************ *************
  port: 1521
  db_name: "HealthOne"
  #sqlplus INTERFACE_LB/INTERFACE_LB@**************:1521/ythis

client:
  tl_client_id: **********365
  client_ip: "**************"
  client_name: "抚州市第三人民医院"
  client_mac: "b8:cb:29:d5:5d:67"

redis:
  host: "127.0.0.1"
  port: 6379
  auth: "123456"
  max_idle: 40
  idle_timeout: 240
  db: 8

nsq:
  lookup_address_list:
    - "*************:4161"
  addr: "*************:4152"

elasticSearch:
  host: "http://*************"
  port: 9200
  username: "elastic"
  password: "xdkj123!@#"
  indexName: "ivankadedicated-api_operation"
