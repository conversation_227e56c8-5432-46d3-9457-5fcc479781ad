package common

import (
	_ "github.com/go-sql-driver/mysql"
	std "gitlab.itingluo.com/backend/ivankastd"
	"log"
)

var (
	GlobalConf *Config
)

type IosPush struct {
	IOSCertificateFileName string `yaml:"certificate_file_name"`
	Password               string `yaml:"password"`
	Development            bool   `yaml:"development"`
	QueueWorks             uint   `yaml:"queue_works"`
	ServiceCount           int    `yaml:"service_count"`
}

type Config struct {
	GoldIosPushConf      IosPush                 `yaml:"gold_ios_push"`
	WeexIosPushConf      IosPush                 `yaml:"weex_ios_push"`
	WitsIosPushConf      IosPush                 `yaml:"wits_ios_push"`
	WitsIosEnterPushConf IosPush                 `yaml:"wits_ios_enter_push"`
	WSCNIosPushConf      IosPush                 `yaml:"wscn_ios_push"`
	WSCNIosProPushConf   IosPush                 `yaml:"wscn_ios_pro_push"`
	XgbIosPushConf       IosPush                 `yaml:"xgb_ios_push"`
	CongIosPushConf      IosPush                 `yaml:"cong_ios_push"`
	CongIosEnterPushConf IosPush                 `yaml:"cong_ios_enter_push"`
	Log                  std.ConfigLog           `json:"logger" yaml:"logger"`
	NSQ                  std.ConfigNSQ           `yaml:"nsq"`
	Redis                std.ConfigRedis         `yaml:"redis"`
	Micro                std.ConfigService       `yaml:"micro"`
	Mysql                std.ConfigMysql         `yaml:"mysql"`
	Mssql                std.ConfigMssql         `yaml:"mssql"`
	Oracle               std.ConfigOracle        `yaml:"oracle"`
	Client               std.ConfigClient        `yaml:"client"`
	ElasticSearch        std.ConfigElasticSearch `json:"elasticSearch" yaml:"elasticSearch"`
}

func InitModel(fileName string) {
	GlobalConf = new(Config)
	std.LoadConf(GlobalConf, fileName)
	std.InitLog(GlobalConf.Log)
	log.Println("InitModel ", GlobalConf)
}
